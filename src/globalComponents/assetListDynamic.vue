<!--资产动态列表格页面-->
<template>
  <Component
    :is="showTree ? ResizeWrapper : 'div'"
    :key="showTree"
    ref="resizeRef"
    :class="[
      showTree ? 'tree-table-wrapper' : '',
      { 'is-product': type == 'product' },
    ]"
  >
    <template #tree>
      <section v-show="showTree">
        <p
          v-if="type == 'manage'"
          style="
            margin-bottom: 15px;
            font-size: 14px;
            font-weight: 500;
            margin-left: 14px;
            overflow: hidden;
            height: 20px;
          "
          class="ml0-new"
        >
          {{ assetTitle }}
        </p>
        <TreeCollection
          ref="deptTree"
          :style="{ height: type == 'manage' ? 'calc(100% - 40px)' : '100%' }"
          :types="[leftTreeType]"
          :spare1="4"
          @getId="getId"
          @getData.once="changeTreeShow"
        ></TreeCollection>
      </section>
    </template>
    <!-- 搜索内容 -->
    <section
      style="overflow-x: hidden"
      class="sel-table-wrapper"
      @click="searchSelectOptions"
    >
      <SelSearch
        v-model="searchData"
        :menu-data="searchOptions.menuData"
        :items="searchOptions.items"
        :show-search-unit-tree="existDeptSearch && !hideSearchUnitTree"
        @search="searchTable"
        @reset="resetSearchValue"
      >
        <ul class="table-action-btns">
          <li v-hasPermi="[permis.add]" class="operate-icon" @click="addAsset">
            <SvgIcon :size="16" icon-class="plus-outline" />
            添加
          </li>
          <li v-hasPermi="[permis.import]" class="operate-icon">
            <SelUploadDialog
              class="upload-button"
              btn-name="导入"
              :show-update-support="false"
              :export-url="downloadTemplateUrl"
              :import-url="importUrl"
              @updateData="searchTable"
            ></SelUploadDialog>
          </li>
          <li
            v-hasPermi="[permis.export]"
            class="operate-icon"
            @click="exportAsset"
          >
            <SvgIcon :size="16" icon-class="undo" />
            导出
          </li>
        </ul>
        <template #searchRight>
          <el-button type="warning" plain @click="openChoseDialog">
            选择展示列
          </el-button>
        </template>
      </SelSearch>
      <div class="table-pagination-new">
        <p v-if="headerSearchTrueData.length > 0" class="checkFont">全部结果</p>
        <div v-if="headerSearchTrueData.length > 0" class="searchList">
          <div v-for="item in headerSearchTrueData" :key="item.field">
            <span>{{ item.title }}：{{ item.searchLabel }}</span>
            <span
              ><SvgIcon
                icon-class="close"
                style="cursor: pointer; transform: scale(1.4)"
                @click="resetNowFileld(item.field)"
            /></span>
          </div>
        </div>

        <el-table
          v-loading="loading"
          :data="tableData"
          style="width: 100%"
          class="mt10"
          @mouseover="tableMouseover"
          @mouseout="tableMouseout"
        >
          <!--  fixed="left" -->
          <el-table-column
            label="资产名称"
            min-width="180"
            fixed="left"
            :class-name="{ 'column-tooltip': type == 'manage' }"
          >
            <template #default="{ row }">
              <div class="sel-clickable" @click="toDetailAsset(row.id)">
                {{ row.assetName }}
              </div>
              <template v-if="type == 'product' && !loading">
                <SelTagList
                  :ref="(el: any) => setRef(el, row.id)"
                  :del-tag="
                    assetType == '1' ? getDeleteAssestTag : getDeleteResourceTag
                  "
                  :check-params="{ id: row.id }"
                  :del-key="
                    assetType == '1'
                      ? 'assestTagRelId'
                      : 'assestResourceTagRelId'
                  "
                  :del-key-name="
                    assetType == '1'
                      ? 'assestTagRelId'
                      : 'assestResourceTagRelId'
                  "
                  :permi="
                    assetType == '1'
                      ? 'vulnAsset:business:deleteAssestTag'
                      : 'vulnAsset:resource:deleteResourceTag'
                  "
                  :list="row.tagNameList"
                  :get-data="
                    assetType == '1'
                      ? getSelectAssestTagById
                      : getSelectResourceTagById
                  "
                  :view-permi="tagViewPer"
                ></SelTagList>
              </template>
            </template>
          </el-table-column>
          <el-table-column
            v-for="item in showfields"
            :key="item"
            :class-name="{ 'column-tooltip': type == 'manage' }"
            :min-width="getStrSize(item.title, item.field) * 17 + 30"
          >
            <template #header
              >{{ item.title }}
              <el-popover
                v-if="
                  !fixedSearchfields.includes(item.field) &&
                  item.field !== 'ipPortProtocolType'
                "
                :visible="item.searchShow"
                placement="top"
                :width="400"
                trigger="click"
              >
                <template #reference>
                  <SvgIcon
                    icon-class="funnel-outline"
                    style="cursor: pointer"
                    :class="item.searchStatus == '1' ? 'searchStatus' : ''"
                    @click.stop="searchSelectOptions(item)"
                  />
                </template>
                <div @click.stop>
                  <div v-if="item.type == 'input'">
                    <el-input
                      v-model="headerSearchData[item.field]"
                      :placeholder="'请输入' + item.title"
                      :type="item.props ? item.props.type : ''"
                      clearable
                      @keyup.enter="
                        item.props && item.props.type == 'textarea'
                          ? null
                          : searchHeaderValue(item)
                      "
                    ></el-input>
                  </div>
                  <div v-if="item.type == 'input-number'">
                    <el-input
                      v-model="headerSearchData[item.field]"
                      :placeholder="'请输入' + item.title"
                      type="number"
                      clearable
                      @keyup.enter="searchHeaderValue(item)"
                    ></el-input>
                  </div>
                  <div
                    v-else-if="
                      item.type == 'select' ||
                      item.type == 'radio' ||
                      item.type == 'checkbox'
                    "
                  >
                    <el-select
                      v-model="headerSearchData[item.field]"
                      style="width: 100%"
                      :placeholder="'请选择' + item.title"
                      :multiple="
                        (item.type == 'select' &&
                          item.props &&
                          item.props.multiple) ||
                        item.type == 'checkbox'
                          ? true
                          : false
                      "
                      clearable
                      filterable
                    >
                      <el-option
                        v-for="option in item.options"
                        :key="option.value"
                        :value="option.value"
                        :label="option.label"
                      ></el-option>
                    </el-select>
                  </div>
                  <div v-else-if="item.type == 'datePicker'">
                    <el-date-picker
                      v-model="headerSearchData[item.field]"
                      style="width: 100%"
                      :type="
                        item.props && item.props.type == 'datetime'
                          ? 'datetimerange'
                          : 'daterange'
                      "
                      :value-format="
                        item.props && item.props.type == 'datetime'
                          ? 'YYYY-MM-DD HH:mm:ss'
                          : 'YYYY-MM-DD'
                      "
                      :format="
                        item.props && item.props.type == 'datetime'
                          ? 'YYYY-MM-DD HH:mm:ss'
                          : 'YYYY-MM-DD'
                      "
                      range-separator=" - "
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      :default-time="
                        item.props && item.props.type == 'datetime'
                          ? [
                              new Date(2000, 1, 1, 0, 0, 0),
                              new Date(2000, 2, 1, 23, 59, 59),
                            ]
                          : undefined
                      "
                    />
                  </div>
                  <div class="text-center" style="margin-top: 20px">
                    <el-button type="primary" @click="searchHeaderValue(item)"
                      >查询</el-button
                    >
                    <el-button @click="resetNowFileld(item.field)"
                      >重置</el-button
                    >
                  </div>
                </div>
              </el-popover>
            </template>
            <template #default="{ row }">
              {{
                item.listType == "0"
                  ? row[item.field]
                  : getSearchLabel(item, row[item.field], "list")
              }}
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
            :width="
              type == 'manage'
                ? $websiteStyle == '1'
                  ? 95
                  : 110
                : $websiteStyle == '1'
                ? 115
                : 130
            "
          >
            <template #default="scope">
              <SelHandleBtns
                ref="btnsRef"
                :btn-list="btnList"
                :scope="scope"
              ></SelHandleBtns>
            </template>
          </el-table-column>
        </el-table>
        <SelPagination
          :total="tableTotal"
          :page-size="pageSize"
          @change="changePagination"
        ></SelPagination>
      </div>
    </section>
    <!-- tooltip -->
    <el-tooltip
      v-model:visible="tooltipVisible"
      :content="tooltipContent"
      placement="top"
      effect="dark"
      trigger="hover"
      virtual-triggering
      :virtual-ref="tooltipTriggerRef"
    />
  </Component>
  <SelDialog
    v-model="chosefieldDialog"
    title="选择展示的列"
    cancel-text="关闭"
    size="large"
    @submit="choseSave"
  >
    <p class="checkFont">
      已选定字段
      <el-popover placement="right-start" :width="200" trigger="hover">
        <div class="margin-inner">可拖拽进行排序</div>
        <template #reference>
          <el-icon><QuestionFilled /></el-icon>
        </template>
      </el-popover>
    </p>

    <section style="padding: 0 20px">
      <VueDraggableNext
        class="dragArea list-group w-full choseFiled"
        :list="chosefields"
        group="site"
        :sort="true"
      >
        <div
          v-for="(item, index) in chosefields"
          :key="item.field"
          class="pointer"
          @click="removeChose(item, index)"
        >
          <span>
            <span>{{ item.title }}</span>
            <SvgIcon
              icon-class="close"
              style="color: var(--primary-red-color)"
            />
          </span>
        </div>
      </VueDraggableNext>
    </section>
    <p class="checkFont">可选字段</p>
    <div style="padding: 0px 20px">
      <div v-for="item in choseAllFieldList" :key="item.type" class="pointer">
        <div v-if="item.type == '0'">
          <p class="checkFont2">资产基础信息</p>
          <div class="filedList">
            <div
              v-for="list in item.list"
              :key="list.field"
              @click="addToChose(list)"
            >
              <span v-if="list.show">
                <span>{{ list.title }}</span>
                <SvgIcon
                  icon-class="plus"
                  style="color: var(--primary-green-color)"
                />
              </span>
            </div>
          </div>
        </div>
        <div>
          <div v-for="list in item.list" :key="list.id">
            <div v-if="list.rule">
              <p class="checkFont2">{{ list.name }}</p>
              <div class="filedList">
                <div
                  v-for="rule in list.rule"
                  :key="rule.field"
                  class="pointer"
                  @click="addToChose(rule)"
                >
                  <span v-if="rule.show">
                    <span>{{ rule.title }}</span>
                    <SvgIcon
                      icon-class="plus"
                      style="color: var(--primary-green-color)"
                    />
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </SelDialog>
  <!-- <SetTag
    v-if="type == 'product' && usePermissionFlag(tagAddPer)"
    ref="setTagRef"
    :type="assetType == '1' ? 'assetsBusiness' : 'assetsComputed'"
    @update="searchTable"
    @updateTags="updateTagOption"
  ></SetTag> -->
  <SelTag
    v-if="showSetTag && type == 'product' && usePermissionFlag(tagAddPer)"
    :get-data="
      assetType == '1' ? getGetListTag : getVulnAssetResourceGetListTag
    "
    :params="{ pageNum: 1, pageSize: 9999, isEvent: 0 }"
    :get-check-data="
      assetType == '1' ? getSelectAssestTagById : getSelectResourceTagById
    "
    :add-tag="assetType == '1' ? postAddAssestTag : postAddAssestResourceTag"
    :del-tag="assetType == '1' ? getDeleteAssestTag : getDeleteResourceTag"
    :check-params="{ id: tagAssetId }"
    :add-params="
      assetType == '1'
        ? { assestId: tagAssetId }
        : { assestResourceId: tagAssetId }
    "
    :select-add-tag="
      assetType == '1' ? postAddAssestTagRel : postAddResourceTagRel
    "
    :del-key="assetType == '1' ? 'assestTagRelId' : 'assestResourceTagRelId'"
    :del-key-name="
      assetType == '1' ? 'assestTagRelId' : 'assestResourceTagRelId'
    "
    :now-event="nowEvent"
    @close="closeSetTag"
    @update="updateTag"
    @update-tag="updateTagOption"
  ></SelTag>
</template>
<script lang="ts" setup name="Assets/list">
import { getGetOptionsByCondition } from "@/api/assets/list";
import {
  getGetListTag,
  postAddAssestTag,
  getDeleteAssestTag,
  getSelectAssestTagById,
  postAddAssestTagRel,
} from "@/api/customer/assets/tagsBusiness";
import {
  getVulnAssetResourceGetListTag,
  postAddAssestResourceTag,
  getDeleteResourceTag,
  getSelectResourceTagById,
  postAddResourceTagRel,
} from "@/api/customer/assets/tagsComputed";
import { getDicts } from "@/api/system/dict/data";
import download from "@/utils/download";
import { VueDraggableNext } from "vue-draggable-next";
import { IStringObject, Btn } from "@/config/types";
import ResizeWrapper from "@/globalComponents/resizeWrapper.vue";
import { useSetAreaDeptParams } from "@/utils/areaDeptParams";

import useTableMouseover from "@/utils/tableMouseover";
import useDeptTreeStore from "@/store/modules/deptTree";
import { getSelectNetInfoListForAsset, postList } from "@/api/assets/network";
import {
  getSelectAssetCloudListForAsset,
  postCloudList,
} from "@/api/assets/cloud";
// import SetTag from "@/views/customer/vulnManagePage/business/setTag.vue";
import useTags from "@/views/customer/vulnManagePage/business/composition/tags";
import usePermissionFlag from "@/utils/permissionFlag";

import useUser from "@/store/modules/user";
const leftTreeType = computed(() => {
  return useUser().leftTreeType;
});

const assetTitle =
  import.meta.env.VITE_APP_IS_DSJ != "true"
    ? "主管范围资产上报台账"
    : "主管范围资产台账";

interface Props {
  type: "product" | "manage"; //类型 product 生产库(资产聚合) ； manage :管理库（安全资产台账）
  assetType?: string; //资产类型
  fixedSearchfields?: string[]; //固定查询条件
  searchOptions: { menuData: any[]; items: any[] }; //查询条件

  activeMenu?: string; //当前激活的菜单

  permis: {
    add: string;
    import: string;
    export: string;
    edit: string;
    del: string;
  }; //选择列展示权限

  getAllColumnsApi: (params?: IStringObject) => Promise<unknown>; //获取全部列接口
  getShowColumsByUser: (params?: IStringObject) => Promise<unknown>; //获取用户展示列接口
  getTableList: (params?: IStringObject) => Promise<unknown>; //获取资产列表接口
  saveShowColums: (params?: IStringObject) => Promise<unknown>; //保存用户选择列
  delApi: (params?: IStringObject) => Promise<unknown>; //删除资产接口

  downloadTemplateUrl: string; //下载模板地址
  importUrl: string; //导入地址
  exportUrl: string; //下载模板地址//导出地址

  deptId?: string | undefined; //单位id，单位详情下的资产台账

  addAssetRouteName?: string; //新增资产路由名称
  editAssetRouteName?: string; //编辑资产路由名称
  detailRouteName?: string; //查看资产详情路由名称
}

const props = withDefaults(defineProps<Props>(), {
  fixedSearchfields: () => [],
  deptId: undefined,
  assetType: "1",
  activeMenu: "",
  addAssetRouteName: "AddAsset",
  editAssetRouteName: "EditAsset",
  detailRouteName: "DetailAsset",
});

const {
  tooltipVisible,
  tooltipContent,
  tooltipTriggerRef,
  tableMouseover,
  tableMouseout,
} = useTableMouseover();
const resizeRef = ref<any>(null);
const hideSearchUnitTree = computed(() => {
  return resizeRef.value && resizeRef.value.hideSearchUnitTree;
});
const existDeptSearch = computed(() => {
  return useDeptTreeStore().existDeptSearch;
});
const router = useRouter();
const route = useRoute();
const deptTree = ref<any>(null);
// 获取单位ID
const { getId, areaDeptParams } = useSetAreaDeptParams(searchTable);

// 表格展示的列内容
const showfields = ref<Array<IStringObject>>([]);
const tableData = ref<Array<IStringObject>>([]);
const tableTotal = ref(0);
const pageSize = ref(10);
const pageNum = ref(1);
// 列表查询遮挡
const loading = ref(false);
const searchData = ref<IStringObject>({});
// 保存表头选择或输入的值
const headerSearchData = ref<IStringObject>({});
// 搜索时传的值
const headerSearchTrueData = ref<IStringObject[]>([]);
// 展开选择弹窗
const chosefieldDialog = ref(false);

const tagViewPer =
  props.assetType == "1"
    ? "vulnAsset:business:getListTag"
    : "vulnAsset:resource:getListTag";
const tagAddPer =
  props.assetType == "1"
    ? "vulnAsset:business:addAssestTag"
    : "vulnAsset:resource:addAssestResourceTag";
// 操作列按钮
const tagAssetId = ref("");
const showSetTag = ref(false);
const nowEvent = ref<any>(null);
const btnList = ref<Btn[]>([
  {
    icon: "pricetags",
    title: "设置标签",
    hide: props.type == "manage",
    hasPermi: tagAddPer,
    onClick({ row }, event: any) {
      tagAssetId.value = row.id;
      nowEvent.value = event;
      if (showSetTag.value) {
        showSetTag.value = false;
      }
      nextTick(() => {
        showSetTag.value = true;
      });
      // openSetTag(row.id);
    },
  },
  {
    icon: "edit",
    title: "编辑",
    hasPermi: props.permis.edit,
    onClick({ row }) {
      router.push({
        name: props.editAssetRouteName,
        params: {
          type: props.type,
          id: row.id,
        },
        query: {
          deptId: props.deptId,
          activeMenu: props.activeMenu,
          assetType: props.assetType,
        },
      });
    },
  },
  {
    icon: "trash-2",
    title: "删除",
    hasPermi: props.permis.del,
    onClick({ row }) {
      props.delApi({ id: row.id, _msg: 1 }).then(() => {
        searchTable();
      });
    },
  },
]);

// 获取所有字段
const allfields = ref<Array<IStringObject>>([]);
function getAllfields() {
  props.getAllColumnsApi().then((res: any) => {
    res[0].list.forEach((item: any) => {
      item.listType = "0";
      //单位详情下的所属单位不展示
      if (item.field == "deptId" && props.deptId) {
        item.show = false;
      } else {
        item.show = true;
      }
    });
    res[1].list.forEach((item: any) => {
      if (item.rule) {
        item.rule = JSON.parse(item.rule);
        item.rule.forEach((rule: any) => {
          rule.listType = "1";

          //单位详情下的所属单位不展示
          if (rule.field == "deptId" && props.deptId) {
            rule.show = false;
          } else {
            rule.show = true;
          }
        });
      }
    });
    allfields.value = res;
    getShowColumsListByUser(allfields.value);
  });
}

searchTable();
// 获取应显示的列
let deptColumnObj: IStringObject = {}; //单位详情下记录是否有所属单位列
function getShowColumsListByUser(allList?: any) {
  props.getShowColumsByUser().then((res) => {
    const data = JSON.parse(res.data);
    if (props.deptId) {
      saveDeptColumnObj(data);
    }

    if (data) {
      showfields.value = data;
      console.log("  showfields.value: ", showfields.value);
      if (allList) {
        showfields.value.forEach((item: any) => {
          allList[1].list.forEach((list: any) => {
            if (list.rule) {
              list.rule.forEach((rule: any) => {
                if (item.field == rule.field) {
                  item.title = rule.title;
                  item.listType = rule.listType;
                  if (item.options) {
                    item.options = rule.options;
                  }
                }
              });
            }
          });
          allList[0].list.forEach((list: any) => {
            if (item.field == list.field) {
              item.title = list.title;
              item.listType = list.listType;
              item.dictKey = list.dictKey;
            }
          });
        });
      }
    } else {
      showfields.value = allfields.value[0];
    }
  });
}
// 已有保存的自定义字段内容查询保存的内容，未保存查询返回资产基本信息
getAllfields();

//是否显示单位树:门户如果只有一个单位不展示；聚合一直展示;单位详情下的资产台账不展示
const showTree = ref(props.deptId ? false : true);
function changeTreeShow(type: string, data: any[]) {
  if (props.type == "product") {
    return;
  }
  if (type == "area-dept") {
    // showTree.value = !!data.length;
    showTree.value = data.length == 0 ? false : !ifOneDept(data);
  }
}
function ifOneDept(list: any[], arr: string[] = []) {
  for (const item of list) {
    if (item.type == 2) {
      if (arr.length > 1) return false;
      arr.push(item.id);
    } else if (Array.isArray(item.children)) {
      const childrenFlag = ifOneDept(item.children, arr);
      if (!childrenFlag) return false;
    }
  }
  return arr.length <= 1;
}

function changePagination(obj: IStringObject) {
  pageSize.value = obj.pageSize;
  pageNum.value = obj.pageNum;
  searchTable();
}
function getSearchData() {
  const arr1: any = [];
  const arr2: any = [];
  const obj1: IStringObject = {};
  const data = JSON.parse(JSON.stringify(headerSearchTrueData.value));
  data.map((item: any) => {
    if (item.listType == "0") {
      arr1.push(item);
    } else if (item.listType == "1") {
      arr2.push(item);
    }
  });
  arr1.forEach((item: any) => {
    if (item.field == "createTime" || item.field == "updateTime") {
      obj1[item.field + "Str"] = item.value[0] + " - " + item.value[1];
    } else {
      obj1[item.field] = item.value;
    }
    if (item.type == "select" || item.type == "radio") {
      if (Array.isArray(item.value)) {
        obj1[item.field] = item.value.join(",");
      } else {
        obj1[item.field] = item.value;
      }
    }
  });
  arr2.forEach((item: any) => {
    delete item.searchShow;
    delete item.searchStatus;
    delete item.show;
    delete item.searchLabel;
  });
  return { data1: obj1, data2: arr2 };
}
function searchTable() {
  if (
    !hideSearchUnitTree.value ||
    (resizeRef.value && !searchData.value.deptTreeId)
  ) {
    useDeptTreeStore().updateDeptId(searchData.value.deptTreeId, route.name);
    if (useDeptTreeStore().existDeptSearch) {
      const deptTreeType =
        useDeptTreeStore().deptSelectData[route.name].deptTreeType;
      areaDeptParams.value.params = {
        deptTreeId: searchData.value.deptTreeId,
        deptTreeType: deptTreeType,
      };
    }
  }
  loading.value = true;
  const obj1 = getSearchData().data1;
  const arr2 = getSearchData().data2;
  const params: IStringObject = {
    deptId: props.deptId,
    ...areaDeptParams.value,
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    ...searchData.value,
    ...obj1,
    customFields: arr2.length > 0 ? JSON.stringify(arr2) : "",
  };
  if (props.deptId) {
    params.deptInside = "1";
  }
  params._abort = 1;
  props.getTableList(params).then((res: any) => {
    res.rows.forEach((item: any) => {
      if (item.customFields) {
        const json = JSON.parse(item.customFields);
        item = Object.assign(item, json);
      }
    });
    tableData.value = res.rows;
    tableTotal.value = res.total;
    loading.value = false;
  });
}
// 重置
function resetSearchValue() {
  getShowColumsListByUser();
  headerSearchData.value = {};
  headerSearchTrueData.value = [];
}
// 展开之前查询下拉内容
function searchSelectOptions(item?: IStringObject) {
  if (item) {
    showfields.value.forEach((list) => {
      if (item.field == list.field) {
        item.searchShow = !item.searchShow;
      } else {
        list.searchShow = false;
      }
    });
    if (
      item.type == "select" ||
      item.type == "checkbox" ||
      item.type == "redio"
    ) {
      if (item.searchShow) {
        if (item.dictKey) {
          if (item.dictKey == "network_type") {
            //网络区域
            if (props.deptId) {
              getSelectNetInfoListForAsset({
                deptId: props.deptId,
              }).then((res) => {
                item.options = getItemOption(res, 3, 1);
              });
            } else {
              postList({ pageSize: 999 }).then((res: any) => {
                item.options = getItemOption(res.rows, 2, 2);
              });
            }
          } else if (item.dictKey == "cloud_platform") {
            // 所属云平台
            if (props.deptId) {
              getSelectAssetCloudListForAsset({
                deptId: props.deptId,
              }).then((res) => {
                item.options = getItemOption(res, 3, 1);
              });
            } else {
              postCloudList({ pageSize: 999 }).then((res: any) => {
                item.options = getItemOption(res.rows, 1, 2);
              });
            }
          } else {
            getDicts(item.dictKey).then((res) => {
              const arr: any = [];
              res.data.forEach((item: any) => {
                arr.push({ value: item.dictValue, label: item.dictLabel });
              });
              item.options = arr;
            });
          }
        } else {
          getGetOptionsByCondition({ field: item.field }).then((res) => {
            item.options = JSON.parse(res.data.options);
          });
        }
      }
    }
  } else {
    showfields.value.forEach((list) => {
      list.searchShow = false;
    });
  }
}
function getItemOption(res: any, type: number, deptType: number) {
  // deptType===1 是通过单位id获取的数据
  if (res) {
    const options = res.map((v: any) => {
      const name = type === 3 ? v.name : "";
      return {
        value: v.id,
        label:
          name ||
          (type === 1 ? v.cloudName : v.netInfo) +
            "(" +
            (deptType === 1 ? v.deptName : v.parentDeptName) +
            ")",
      };
    });

    return options;
  } else {
    return [];
  }
}
// 打开选择列弹窗
// 已选定字段
const chosefields = ref<IStringObject[]>([]);
const choseAllFieldList = ref<IStringObject[]>([]);
function openChoseDialog() {
  chosefieldDialog.value = true;
  chosefields.value = JSON.parse(JSON.stringify(showfields.value));
  choseAllFieldList.value = JSON.parse(JSON.stringify(allfields.value));

  chosefields.value.forEach((item: any) => {
    changeShow(item.field, "update");
  });
}

// 添加到已选定字段
function addToChose(item: IStringObject) {
  changeShow(item.field, "add");
  chosefields.value.push(item);
}
// 移除选项
function removeChose(item: IStringObject, index: number) {
  changeShow(item.field, "remove");
  chosefields.value.splice(index, 1);
}
// 切换未选定内容的显示隐藏
function changeShow(field: string, type: string) {
  choseAllFieldList.value.forEach((el) => {
    if (el.type == "0") {
      el.list.forEach((list: any) => {
        if (type == "add" && list.field == field) {
          list.show = false;
        }
        if (type == "update") {
          if (list.field == field) {
            list.show = false;
          }
        }
        if (type == "remove" && list.field == field) {
          list.show = true;
        }
      });
    }
    if (el.type == "1") {
      el.list.forEach((list: any) => {
        if (list.rule) {
          list.rule.forEach((newList: any) => {
            if (type == "add" && newList.field == field) {
              newList.show = false;
            }
            if (type == "update") {
              if (newList.field == field) {
                newList.show = false;
              }
            }
            if (type == "remove" && newList.field == field) {
              newList.show = true;
            }
          });
        }
      });
    }
  });
}

// 选择后保存
function choseSave() {
  const arr = JSON.parse(JSON.stringify(chosefields.value));
  arr.forEach((item: any) => {
    delete item.searchShow;
    delete item.searchStatus;
    delete item.show;
  });
  if (props.deptId) {
    handlerDeptColumn(arr);
  }

  props
    .saveShowColums({
      showColumns: JSON.stringify(arr),
      _msg: 1,
    })
    .then(() => {
      chosefieldDialog.value = false;
      getShowColumsListByUser();
    });
}

//单位详情下保存是否有所属单位列，保存数据
function saveDeptColumnObj(data: any[]) {
  data.find((item: any, index: number) => {
    if (item.field == "deptId") {
      deptColumnObj = { ...item, index };
      data.splice(index, 1);
      return true;
    }
  });
}
// 单位详情下的保存展示列判断是否添加所属单位
function handlerDeptColumn(arr: any[]) {
  if ("index" in deptColumnObj) {
    //根据index的值是否小于arr的长度，将deptColumnObj插入到arr的index位置，或者最后一位
    if (deptColumnObj.index < arr.length) {
      arr.splice(deptColumnObj.index, 0, deptColumnObj);
    } else {
      arr.push(deptColumnObj);
    }
  }
}
// 重置当前表格头搜索值
function resetNowFileld(field: string) {
  delete headerSearchData.value[field];
  let num = -1;
  headerSearchTrueData.value.forEach((item, index) => {
    if (item.field == field) {
      num = index;
    }
  });
  if (num >= 0) {
    headerSearchTrueData.value.splice(num, 1);
  }
  showfields.value.map((item) => {
    if (item.field == field) {
      item.searchStatus = "0";
    }
  });
  searchTable();
}
// 查询表格头搜索
function searchHeaderValue(item: IStringObject) {
  let isNull = false;
  if (
    (item.type == "checkbox" ||
      item.type == "select" ||
      item.type == "radio") &&
    headerSearchData.value[item.field].length <= 0
  ) {
    isNull = true;
  }
  if (
    item.type != "checkbox" &&
    item.type != "select" &&
    item.type != "radio" &&
    !headerSearchData.value[item.field]
  ) {
    isNull = true;
  }
  if (isNull) {
    ElMessage.warning("请输入或选择搜索内容！");
    return false;
  }
  let flag = true;
  headerSearchTrueData.value.forEach((list) => {
    if (list.field == item.field) {
      flag = false;
      list.value = headerSearchData.value[item.field];
      list.searchLabel = getSearchLabel(
        item,
        headerSearchData.value[item.field],
        "search"
      );
    }
  });
  if (flag) {
    let value: any = headerSearchData.value[item.field];
    if (
      (item.type == "select" || item.type == "radio") &&
      typeof value == "string"
    ) {
      value = [value];
    }
    headerSearchTrueData.value.push({
      ...item,
      value,
      searchLabel: getSearchLabel(
        item,
        headerSearchData.value[item.field],
        "search"
      ),
    });
  }
  item.searchShow = false;
  item.searchStatus = "1";
  searchTable();
}
function getSearchLabel(item: IStringObject, value?: any, type?: string) {
  let str: any = "";
  let thisValue: any = null;
  if (type == "list") {
    if (value) {
      thisValue = value;
    } else {
      if (item.type == "checkbox") {
        thisValue = [];
      } else {
        thisValue = "";
      }
    }
  } else if (type == "search") {
    if (value) {
      thisValue = value;
    }
  } else {
    thisValue = headerSearchData.value[item.field];
  }
  if (thisValue) {
    if (type == "list" && item.listType == "1") {
      if (
        item.type == "checkbox" ||
        (item.type == "select" && item.props && item.props.multiple)
      ) {
        thisValue.forEach((value: any) => {
          item.options.forEach((option: any) => {
            if (option.value == value) {
              if (str) {
                str = str + "," + option.label;
              } else {
                str += option.label;
              }
            }
          });
        });
      } else if (
        (item.type == "select" && (!item.props || !item.props.multiple)) ||
        item.type == "radio"
      ) {
        item.options.forEach((option: any) => {
          if (option.value == thisValue) {
            if (str) {
              str = str + "," + option.label;
            } else {
              str += option.label;
            }
          }
        });
      } else {
        str = thisValue;
      }
    } else if (type == "search") {
      if (item.type == "input" || item.type == "input-number") {
        str = thisValue;
      } else if (item.type == "datePicker") {
        str = thisValue[0] + "-" + thisValue[1];
      } else {
        let valueArr: any[] = [];
        if (thisValue && !Array.isArray(thisValue)) {
          valueArr = [thisValue];
        } else {
          valueArr = thisValue;
        }
        valueArr.forEach((value: any) => {
          item.options.forEach((option: any) => {
            if (option.value == value) {
              if (str) {
                str = str + "," + option.label;
              } else {
                str += option.label;
              }
            }
          });
        });
      }
    } else {
      str = thisValue;
    }
  } else {
    str = "";
  }
  return str;
}
// 跳转到资产详情
function toDetailAsset(id: string) {
  const aaa = {
    name: props.detailRouteName,
    params: {
      type: props.type,
      id: id,
    },
    query: {
      activeMenu: props.activeMenu,
      assetType: props.assetType,
    },
  };
  console.log("aaa: ", aaa);
  const href = `/assets/detail/${id}/${props.type}?activeMenu=${props.activeMenu}&assetType=${props.assetType}`;
  console.log("href: ", href);
  router.push(aaa);
}
// 添加资产
function addAsset() {
  let deptId = "";
  if (props.deptId) {
    deptId = props.deptId;
  } else {
    if (
      areaDeptParams.value.params &&
      areaDeptParams.value.params.deptTreeType == 2
    ) {
      deptId = areaDeptParams.value.params.deptTreeId;
    }
  }

  router.push({
    name: props.addAssetRouteName,
    params: {
      type: props.type,
    },
    query: {
      deptId,
      activeMenu: props.activeMenu,
      assetType: props.assetType,
    },
  });
}

// 计算字符串长度
function getStrSize(value: string, field: string) {
  if (["url", "domain", "updateTime"].includes(field)) {
    return 9;
  }

  if (!value) {
    return 0;
  }
  const charCount = value.split("").reduce((prev, curr) => {
    if (/[a-z]|[0-9]|[,;.!@#-+/\\$%^*()<>?:"'{}~]/i.test(curr)) {
      return prev + 2;
    }
    return prev + 3;
  }, 0);

  // 向上取整，防止出现半个字的情况
  return Math.ceil(charCount / 2);
}
watch(
  () => route.path,
  () => {
    searchTable();
  }
);
watch(areaDeptParams, () => {
  searchData.value.deptTreeId = areaDeptParams.value.params.deptTreeId;
});

// 导出资产内容
function exportAsset() {
  const obj1 = getSearchData().data1;
  const arr2 = getSearchData().data2;
  const params: any = {
    ...areaDeptParams.value,
    ...searchData.value,
    ...obj1,
    customFields: arr2.length > 0 ? JSON.stringify(arr2) : "",
  };
  if (props.deptId) {
    params.deptId = props.deptId;
    params.deptInside = "1";
  }
  download(
    props.exportUrl,
    params,
    `资产列表_${new Date().getTime()}.xlsx`,
    true
  );
}

// //标签
const { updateTagOption } = useTags(
  props.searchOptions.items,
  props.assetType == "1" ? "assetsBusiness" : "assetsComputed"
);
// 关闭弹出内容
function closeSetTag() {
  showSetTag.value = false;
  tagAssetId.value = "";
}
const tagRefs = ref<any>({});
// 选择后更新
function updateTag() {
  tagRefs.value[tagAssetId.value].update();
}
function setRef(el: any, deptId: string) {
  if (el) {
    tagRefs.value[deptId] = el;
  }
}
</script>
<style lang="scss" scoped>
@import "@/assets/styles/assetList.scss";
.is-product {
  :deep(.search-reset-box) {
    justify-content: flex-start;
  }
}
</style>
