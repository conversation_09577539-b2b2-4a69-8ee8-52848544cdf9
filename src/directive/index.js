import hasRole from "./permission/hasRole";
import hasPermi from "./permission/hasPermi";
import copyText from "./common/copyText";
import btnDisabled from "./common/btnDisabled";
import setTagRowWidth from "./common/setTagRowWidth";
import clickDept from "./common/clickDept";

import customInstruction from "./common/customInstruction";

import hasKey from "./permission/hasKey";
export default function directive(app) {
  app.directive("hasRole", hasRole);
  app.directive("hasPermi", hasPermi);
  app.directive("copyText", copyText);
  app.directive("btnDisabled", btnDisabled);
  app.directive("hasKey", hasKey);
  app.directive("setTagRowWidth", setTagRowWidth);
  app.directive("clickDept", clickDept);

  app.directive("preventRepeatClick", customInstruction);
}
