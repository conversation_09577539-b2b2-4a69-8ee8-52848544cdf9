<template>
  <section class="sel-table-wrapper" style="overflow-x: hidden">
    2
    <SelSearch
      v-if="searchOption"
      v-model="searchData"
      v-bind="searchOption"
      :show-search-unit-tree="showSearchUnitTree"
      @search="search(true)"
      @reset="resetChange"
    >
      <ul class="table-action-btns">
        <li
          v-for="(btn, index) in btnList.filter((item) => !item.hide)"
          :key="index"
          v-hasPermi="btn.hasPermi ? [btn.hasPermi] : null"
          class="operate-icon"
          @click="actionBtn(btn)"
        >
          <!-- 导入 -->
          <SelUploadDialog
            v-if="btn.option && btn.option.importUrl"
            class="upload-button"
            :btn-name="btn.title"
            size="70px"
            v-bind="btn.option"
            @updateData="updateData"
          >
          </SelUploadDialog>
          <template v-else>
            <SvgIcon :size="16" :icon-class="getIconName(btn.icon)" />
            {{ btn.title }}
          </template>
        </li>
      </ul>
      <slot name="searchSlot"></slot>
      <template #searchRight>
        <slot name="searchRight"></slot>
      </template>
    </SelSearch>
    <div class="table-pagination-new">
      <ul v-if="!searchOption" class="table-action-btns mb10">
        <li
          v-for="(btn, index) in btnList.filter((item) => !item.hide)"
          :key="index"
          v-hasPermi="btn.hasPermi ? [btn.hasPermi] : null"
          class="operate-icon"
          @click="actionBtn(btn)"
        >
          <!-- 导入 -->
          <SelUploadDialog
            v-if="btn.option && btn.option.importUrl"
            class="upload-button"
            :btn-name="btn.title"
            size="70px"
            :export-title="btn.option.exportTitle"
            v-bind="btn.option"
            @updateData="search"
          >
          </SelUploadDialog>
          <template v-else>
            <SvgIcon :size="16" :icon-class="getIconName(btn.icon)" />
            {{ btn.title }}
          </template>
        </li>
      </ul>
      <el-table
        ref="table"
        v-loading="loading"
        :data="state.list"
        v-bind="$attrs"
        style="width: 100%"
        @sortChange="sortChange"
        @selection-change="handleSelectionChange"
        @mouseover="tableMouseover"
        @mouseout="tableMouseout"
        @header-click="handleHeaderClick"
      >
        <el-table-column
          v-if="serialNumber"
          type="index"
          width="55"
        ></el-table-column>
        <el-table-column
          v-if="checkbox"
          :reserve-selection="true"
          type="selection"
          width="55"
          :selectable="selecTable || null"
        ></el-table-column>

        <template
          v-for="(columnItem, index) in columns.filter((item) => !item.hide)"
          :key="index"
        >
          <!-- 渲染为html的列 -->
          <el-table-column
            v-if="columnItem.html"
            :class-name="showToolTip ? 'column-tooltip' : ''"
            v-bind="columnItem"
          >
            <template #default="scope">
              <div v-html="$replaceToken(scope.row[columnItem.prop])"></div>
            </template>
          </el-table-column>
          <!-- 状态列  -->
          <el-table-column v-else-if="columnItem.isStatus" v-bind="columnItem">
            <template #default="scope">
              <SelStatus
                :value="scope.row[columnItem.prop]"
                :option="columnItem.option"
              />
            </template>
          </el-table-column>

          <!-- tag字典值列 -->
          <el-table-column v-else-if="columnItem.dictType" v-bind="columnItem">
            <template #default="scope">
              <DictTag
                :dict-type="columnItem.dictType"
                :value="scope.row[columnItem.prop] + ''"
              />
            </template>
          </el-table-column>
          <!-- 开关列 -->
          <el-table-column v-else-if="columnItem.isSwitch" v-bind="columnItem">
            <template #default="scope">
              <el-switch
                v-model="scope.row[columnItem.prop]"
                v-bind="columnItem.switchOption"
                @change="(val:any) => changeSwitch(val,columnItem, scope.row)"
              />
            </template>
          </el-table-column>
          <!-- 进度条 -->
          <el-table-column
            v-else-if="columnItem.isProgress"
            v-bind="columnItem"
          >
            <template #default="scope">
              <div class="progress-box">
                <div class="bar-box">
                  <span class="rate-span"
                    >{{ scope.row[columnItem.prop as string] }}%</span
                  >
                  <el-progress
                    :percentage="scope.row[columnItem.prop as string] * 1"
                    v-bind="columnItem.progressOption"
                    :format="() => ''"
                  />
                </div>
                <span>{{
                  scope.row[columnItem.progressOption.numProp as string]
                }}</span>
              </div>
            </template>
          </el-table-column>
          <!-- 等级列 -->
          <el-table-column v-else-if="columnItem.isLevel" v-bind="columnItem">
            <template #default="scope">
              <el-tag :type="LEVEL_DATA[scope.row[columnItem.prop]]">{{
                scope.row[columnItem.propName]
              }}</el-tag>
            </template>
          </el-table-column>
          <!-- 多条数据 -->
          <el-table-column
            v-else-if="columnItem.isMultiple"
            v-bind="columnItem"
          >
            <template #default="scope">
              <SelMultipleData
                :row-data="scope.row[columnItem.prop]"
                :options="
                  columnItem.multipleOption
                    ? {
                        ...columnItem.multipleOption,
                        total: columnItem.multipleOption.multipleProp
                          ? scope.row[columnItem.multipleOption.multipleProp]
                          : null,
                        showMultipleTotal:
                          scope.row[columnItem.multipleOption.showProp] ==
                          columnItem.multipleOption.showValue,
                      }
                    : {}
                "
              ></SelMultipleData>
            </template>
          </el-table-column>
          <!-- 继承插槽 -->
          <el-table-column
            v-else-if="columnItem.slotName"
            :class-name="
              columnItem.label != '操作' && showToolTip ? 'column-tooltip' : ''
            "
            :fixed="columnItem.label == '操作' ? 'right' : false"
            :label="columnItem.slotName == 'action' ? '操作' : columnItem.label"
            v-bind="columnItem"
            :width="
              columnItem.slotName == 'action'
                ? columnItem.btnsWidth
                  ? columnItem.btnsWidth
                  : actionWidth
                : columnItem.width
            "
          >
            <template #default="scope">
              <!-- 操作列按钮组插槽 -->
              <SelHandleBtns
                v-if="columnItem.slotName == 'action'"
                ref="btnsRef"
                :btn-list="columnItem.btnList"
                :btn-type="columnItem.btnType ? columnItem.btnType : 'icon'"
                :scope="scope"
              ></SelHandleBtns>
              <!-- 普通插槽 -->
              <slot
                v-if="columnItem.slotName != 'action' && scope.$index > -1"
                :name="columnItem.slotName"
                :row="scope.row"
                :column="scope.column"
                :index="scope.$index"
              ></slot>
            </template>
          </el-table-column>
          <!-- 可点击的列 -->
          <el-table-column
            v-else-if="columnItem.onClick"
            :class-name="showToolTip ? 'column-tooltip' : ''"
            v-bind="columnItem"
          >
            <template #default="scope">
              <div
                class="sel-clickable"
                @click="columnOnClick(columnItem, scope)"
              >
                {{
                  columnItem.formatter
                    ? columnItem.formatter(scope)
                    : scope.row[columnItem.prop]
                }}
              </div>
            </template>
          </el-table-column>
          <!-- 跳转到单位详情页面 -->
          <el-table-column
            v-else-if="
              columnItem.prop == 'deptName' ||
              columnItem.prop == 'eventTitle' ||
              columnItem.clickDept
            "
            v-hasKey="columnItem.props"
            :class-name="showToolTip ? 'column-tooltip' : ''"
            v-bind="columnItem"
          >
            <template #default="scope">
              <div
                v-clickDept="[
                  columnItem?.clickDept
                    ? columnItem.clickDept
                    : columnItem.prop,
                  scope.row,
                ]"
                class="sel-clickable"
              >
                跳转{{ scope.row[columnItem.prop] }}
              </div>
            </template></el-table-column
          >
          <!-- 正常的el-table-column -->
          <el-table-column
            v-else
            v-hasKey="columnItem.props"
            :class-name="showToolTip ? 'column-tooltip' : ''"
            v-bind="columnItem"
          ></el-table-column>
        </template>
      </el-table>
      <!-- 显示名称 -->
      <div class="dialog-footer" style="overflow: hidden">
        <div class="Count">
          <slot name="tableFooter"> </slot>
        </div>
        <SelPagination
          v-if="selfShowPagination"
          ref="paginationRef"
          class="sel-table-pagination"
          :total="total"
          :page-size="pageSize"
          :page-sizes="pageSizes"
          @change="changePagination"
        ></SelPagination>
      </div>
    </div>
    <!-- tooltip -->
    <el-tooltip
      v-model:visible="tooltipVisible"
      :content="tooltipContent"
      placement="top"
      effect="dark"
      trigger="hover"
      virtual-triggering
      :virtual-ref="tooltipTriggerRef"
    />
  </section>
</template>
<script lang="ts">
export default {
  inheritAttrs: false,
};
</script>
<script setup lang="ts">
import type { ElTable } from "element-plus";

import usePermissionFlag from "@/utils/permissionFlag";

import type {
  ColumnItem,
  Btn,
  MenuData,
  FormItem,
  Scope,
} from "@/config/types";

import download from "@/utils/download";

import type {
  PageInfo,
  IStringObject,
  StateData,
  LastSearchParams,
  SortParams,
} from "./types";
import SelHandleBtns from "../selHandleBtns/index.vue";
import SelPagination from "../selPagination/index.vue";
import SelMultipleData from "./selMultipleData/index.vue";
import Sortable from "sortablejs";

import getIconName from "@/utils/getIconName";
import { LEVEL_DATA } from "@/config/constant";

import useTableMouseover from "@/utils/tableMouseover";
const {
  tooltipVisible,
  tooltipContent,
  tooltipTriggerRef,
  tableMouseover,
  tableMouseout,
} = useTableMouseover();

const attrs = useAttrs();

interface SearchProps {
  modelValue: IStringObject; //表单绑定的数据
  menuData?: MenuData[];
  items?: FormItem[]; //表单项列表
  gutter?: number; //布局间隔 默认40
  span?: number; //默认每行几个表单项 默认6
  btnsSpan?: number; //查询充值按钮span
  showFilter?: boolean; //是否默认展开搜索查询
  isUnit?: boolean; //是否搜索单位
}

const { proxy } = getCurrentInstance();

type ElTableProps = InstanceType<typeof ElTable>["$props"];
interface Props extends ElTableProps {
  columns: ColumnItem[]; //传入的列数组
  loadData?: (params: IStringObject) => Promise<any>; //通过调用接口加载表格数据
  data?: IStringObject[]; //不通过接口传入静态表格数据数组
  handleData?: <T>(list: T) => T; //处理返回数据的函数
  serialNumber?: boolean; //是否显示序号
  checkbox?: boolean; //是否显示复选框
  selecTable?: (a: any, b?: any) => boolean | null;
  showPagination?: boolean | undefined; //是否显示分页
  pageSize?: number; //每页几条
  pageSizes?: number[]; //分页列表
  defaultParams?: IStringObject; //请求数据接口携带的额外参数
  resKey?: string; //接口返回的列表key
  totalKey?: string; //接口返回的totalkey
  dropEnable?: boolean; //是否可拖拽
  searchOption?: SearchProps | undefined; //查询配置
  btnList?: Btn[]; //表格上方的按钮
  showSearchUnitTree?: boolean; //是否和左侧树单位联动
  showToolTip?: boolean; //是否显示tooltip，默认显示
  handleQueryParams?: (params: IStringObject) => IStringObject; //处理查询参数
}

const props = withDefaults(defineProps<Props>(), {
  btnList: () => {
    return [];
  },
  loadData: undefined,
  data: undefined,
  handleData: undefined,
  checkbox: false,
  selecTable: undefined,
  showPagination: undefined,
  pageSize: 10,
  pageSizes: () => [10, 20, 50, 100],
  defaultParams: () => {
    return {};
  },
  resKey: "rows",
  totalKey: "total",
  dropEnable: false,
  searchOption: undefined,
  showSearchUnitTree: false, //是否和左侧树单位联动
  showToolTip: true,
});

const searchData = ref<IStringObject>({});
if (props.searchOption) {
  searchData.value = props.searchOption;
}

//供父组件访问el-table
const total = ref(0);
const staticTotal = ref(0); //第一次请求的总量，不跟随查询条件改变
const table = ref<InstanceType<typeof ElTable>>(); //声明table ref

const loading = ref(false);
let loadingTimer: any = null; // 避免abort引起的暂无数据

const state: StateData = reactive({
  list: [],
  resData: {},
  checkList: [],
});

const paginationRef = ref();
//是否显示分页 手动传入showPagination=false 不显示。非接口数据data不显示
const selfShowPagination = ref(true);
selfShowPagination.value =
  props.showPagination === undefined || props.showPagination === true;
if (props.showPagination) {
  selfShowPagination.value = false;
}

let searchParams = {};
let pageParams: PageInfo = {};

let lastParams: LastSearchParams = {}; //排序使用
const sortParams: SortParams = {};

//设置表格数据
const emit = defineEmits([
  "tableLoaded",
  "selectChange",
  "selectChangeFlag",
  "clickSearch",
  "reset",
  "handleHeaderClick",
  "uploadUpdate",
  "changePagination",
]);
function getData(params: IStringObject = {}, initPageNum = true) {
  loading.value = true;

  if (props.data) {
    state.list = props.data;
    loading.value = false;

    return;
  }

  //如果有参数，分页重置为1
  if (selfShowPagination.value && initPageNum && paginationRef.value) {
    paginationRef.value.resetPageNum();
    //清空复选框选中
    props.checkbox && table.value?.clearSelection();
  }

  if (paginationRef.value) {
    pageParams = paginationRef.value.pageData;
  }

  searchParams = params ? { ...params } : {};

  lastParams = {
    ...props.defaultParams,
    ...searchParams,
    ...sortParams,
    ...pageParams,
    _allowRepeat: 1,
  };

  if (props.handleQueryParams) {
    lastParams = props.handleQueryParams(lastParams);
  }
  //处理左侧菜单树的查询条件！注意：左侧树以defaultParams参数为准，避免和查询组件的查询条件覆盖冲突
  if (
    "params" in props.defaultParams &&
    "deptTreeType" in props.defaultParams.params
  ) {
    if (lastParams.params) {
      lastParams.params.deptTreeType = props.defaultParams.params.deptTreeType;
    } else {
      lastParams.params = {
        deptTreeType: props.defaultParams.params.deptTreeType,
      };
    }
  }
  if (
    "params" in props.defaultParams &&
    "deptTreeId" in props.defaultParams.params
  ) {
    if (lastParams.params) {
      lastParams.params.deptTreeId = props.defaultParams.params.deptTreeId;
    } else {
      lastParams.params = {
        deptTreeId: props.defaultParams.params.deptTreeId,
      };
    }
  }

  props
    .loadData(lastParams)
    .then((data: IStringObject) => {
      if (props.resKey) {
        state.list = data[props.resKey];
      } else {
        state.list = data.rows || data;
      }
      state.resData = data;
      if (props.handleData) {
        state.list = props.handleData(state.list); //处理返回数据
      }

      total.value = data[props.totalKey] || 0;

      //保存没有查询条件的总量
      if (!staticTotal.value) {
        staticTotal.value = total.value;
      }
      //删除后页码后退一个
      if (
        selfShowPagination.value &&
        state.list.length == 0 &&
        pageParams.pageNum != 1
      ) {
        paginationRef.value.pageNum--;
      }
      loading.value = false;
    })
    .catch((e) => {
      if (e.apiAbort) {
        return;
      }
      loadingTimer && clearTimeout(loadingTimer);
      loadingTimer = setTimeout(() => {
        loading.value = false;
      }, 200);
    })
    .finally((e) => {
      emit("tableLoaded");
    });
}

let refreshStatus = false; //keep-alive刷新

onMounted(() => {
  init();
});
function init() {
  //需要从路由获取查询条件时，查询组件内获取数据
  if (props.searchOption?.getRouteQuery) {
    setTimeout(() => {
      refreshStatus = true;
    }, 100);
  } else {
    getData();
    setTimeout(() => {
      refreshStatus = true;
    });
  }
}

//有查询条件的，页面active时自动查询，否则需要手动调用reload
onActivated(() => {
  if (refreshStatus) {
    if (props.searchOption && props.searchOption.modelValue) {
      getData(props.searchOption.modelValue, false);
    }
  }
});

//分页改变 ，初始化加载数据
function changePagination(params: PageInfo) {
  pageParams = { ...params };

  const searchP = getSearchParams();

  getData(searchP, false);
  nextTick(() => {
    emit("changePagination", params);
  });
}
//分页切换时获取搜索条件
function getSearchParams() {
  const lasetSearchParams = { ...lastParams };
  delete lasetSearchParams.pageNum;
  delete lasetSearchParams.pageSize;
  delete lasetSearchParams.orderByColumn;
  delete lasetSearchParams.isAsc;
  return lasetSearchParams;
}

//表格操作列的宽度自适应
const btnsRef = ref();
const actionWidth = ref();

const btnWatch = watch(
  () => btnsRef.value,
  () => {
    if (Array.isArray(btnsRef.value) && btnsRef.value.length > 0) {
      if (btnsRef.value[0].$el) {
        let maxWidth = Math.max(
          ...btnsRef.value.map((item) => {
            return item.$el ? item.$el.offsetWidth : 0;
          })
        );
        if (maxWidth < 40) {
          maxWidth = 40;
        }
        actionWidth.value = maxWidth + 26 + "px";
      }

      nextTick(() => {
        table.value?.doLayout();
      });
      btnWatch();
    }
  }
);

// 行拖拽
function rowDrop() {
  // 此时找到的元素是要拖拽元素的父容器
  const tbody = document.querySelector(".el-table__body-wrapper tbody");

  Sortable.create(tbody, {
    //  指定父元素下可被拖拽的子元素
    draggable: ".el-table__row",
    onEnd({ newIndex, oldIndex }: { newIndex: number; oldIndex: number }) {
      const currRow = state.list.splice(oldIndex, 1)[0];
      state.list.splice(newIndex, 0, currRow);
    },
  });
}
//data数据变化时更新表格
watch(
  () => props.data,
  () => {
    getData();
    if (props.dropEnable) {
      rowDrop();
    }
  }
);

//排序
const sortChange: (payload: IStringObject) => void = function ({
  prop,
  order,
}) {
  sortParams.orderByColumn = prop;
  sortParams.isAsc = order == "descending" ? "desc" : "asc";
  getData({ ...lastParams, ...sortParams });
};

function changeSwitch(val: any, columnItem: ColumnItem, row: any): void {
  const {
    prop,
    switchOption: {
      activeValue,
      inactiveValue,
      onText,
      offText,
      submitFn,
      confirmDialog = true,
    },
  } = columnItem;

  const status: number | string = row[prop as string];
  const text = status === activeValue ? onText : offText;

  if (confirmDialog) {
    proxy.$modal
      .confirm(`确认${text}吗?`)
      .then(function () {
        return submitFn(row, () => {
          proxy.$modal.msgSuccess(text + "成功");
        });
      })
      .catch(function () {
        row[prop as string] =
          status === activeValue ? inactiveValue : activeValue;
      });
  } else {
    submitFn(row, () => {
      proxy.$modal.msgSuccess(text + "成功");
    });
  }
}
//查询按钮
function search(initPage = false) {
  if (props.searchOption?.modelValue) {
    getData(props.searchOption.modelValue, initPage);
    emit("clickSearch");
  }
}
function updateData() {
  search();
  emit("uploadUpdate");
}
//列点击事件,添加权限控制
function columnOnClick(columnItem: ColumnItem, scope: Scope) {
  const permissionFlag = usePermissionFlag(columnItem.hasPermi);
  if (permissionFlag) {
    columnItem.onClick && columnItem.onClick(scope);
  }
}

//按钮点击
function actionBtn(btn: any) {
  if (btn.option) {
    if (btn.option.importUrl) {
      //导入
      return;
    } else if (btn.option.exportUrl) {
      let params = { ...props.searchOption.modelValue, ...btn.option.params };
      if (btn.option.getParams) {
        const dynamics = btn.option.getParams(); //获取相应式参数
        params = { ...params, ...dynamics };
      }
      //导出
      download(
        btn.option.exportUrl,
        params,
        `${btn.option.exportTitle || "导出"}_${new Date().getTime()}.xlsx`,
        true
      );
    } else if (btn.option.delApi) {
      batchHandle("删除").then(({ ids }) => {
        let param = null;
        if (btn.option.defaultParams) {
          param = { ids: ids, ...btn.option.defaultParams, _msg: 1 };
        } else {
          param = { ids, _msg: 1 };
        }
        /* 删除 - 增加 paramKey 替换 */
        if (btn.option.paramKey !== "ids") {
          param[btn.option.paramKey] = param.ids;
          if (btn.option.isString) {
            param[btn.option.paramKey] = param.ids.join(",");
          }
          delete param.ids;
        }

        btn.option.delApi(param).then(() => {
          getData(props.searchOption.modelValue, false);
          clearSelection();
        });
      });
    }
  } else {
    btn.onClick && btn.onClick();
  }
}
// 多选值内容
function handleSelectionChange(val: Array<object>) {
  state.checkList = val;
  emit("selectChange", val); //继承el-table的@selectChange事件
}
//全选、全不选
function selectAll(flag: boolean) {
  state.list.map((row) => {
    table.value?.toggleAllSelection(row, flag);
  });
}
//批量操作函数
async function batchHandle(msgText?: string, showConfirm = true) {
  const ids = (state.checkList || []).map(
    (item) => item[(attrs["row-key"] as string) || "id"]
  );
  const rows = table.value?.getSelectionRows();
  if (ids.length == 0) {
    ElMessage.warning("请先选择数据");
    return Promise.reject(false);
  }
  if (!showConfirm) {
    return { ids, rows };
  } else {
    //批量删除
    const res: boolean = await ElMessageBox.confirm(
      `确认${msgText}选中数据？`,
      "警告",
      {
        distinguishCancelAndClose: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    );
    if (res) {
      return { ids, rows };
    } else {
      return Promise.reject(false);
    }
  }
}
function clearSelection() {
  table.value?.clearSelection();
}

//  表头点击事件
function handleHeaderClick(val: Array<object>) {
  emit("handleHeaderClick", val);
}
// 重置转态
function resetChange() {
  emit("reset");
}
//! 只用于测试漏洞页面，不属于组件通用功能监听state.checkList 变化,
watch(
  () => state.checkList,
  (val: any) => {
    const checkedCount = val.length;
    const flag = checkedCount > 0 && checkedCount < state.list.length;
    const flag2 = checkedCount === state.list.length;
    emit("selectChangeFlag", { flag: flag, flag2: flag2 });
  }
);

//父组件可以调用的方法
defineExpose({
  //el-table组件，可以调用el-table的方法
  table: computed(() => {
    return table.value;
  }),
  //加载数据
  reload: getData,
  //表格当前数据数组
  data: computed(() => {
    return state.list || [];
  }),
  //接口返回数据
  resData: computed(() => {
    return state.resData;
  }),
  //当前total
  total: computed(() => {
    return total.value;
  }),
  //第一次获取的total
  staticTotal: computed(() => {
    return staticTotal.value;
  }),
  checkList: computed(() => {
    return state.checkList;
  }),
  search,
  batchHandle,
  clearSelection,
  selectAll,
});
</script>

<style lang="scss" scoped>
.sel-table-wrapper {
  overflow-x: hidden;
}
.dialog-footer {
  width: 100%;
  // display: flex;
  // justify-content: space-between;
}
.Count {
  width: 100%;
  float: left;
  line-height: 32px;
  background: #fff;
  padding: 15px 15px 0 15px;
}

.progress-box {
  display: flex;
  align-items: center;
  .bar-box {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    flex: 1;
    margin-top: -9px;
    & + span {
      width: 40px;
      transform: translateY(6px);
    }
  }
  .rate-span {
    transform: scale(0.9);
    transform-origin: center bottom;
  }
  :deep(.el-progress) {
    display: inline-block;
    width: 90%;
  }
}
:deep(.large-expand) {
  .el-icon {
    transform: scale(1.3);
  }
}
:deep(.el-table-fixed-column--right) {
  background: #fff !important;
}

:deep(.hover-row .el-table-fixed-column--right) {
  background: rgba(244, 247, 251, 1) !important;
}
:deep(.action-btns-ul) {
  display: inline-flex;
}
</style>
