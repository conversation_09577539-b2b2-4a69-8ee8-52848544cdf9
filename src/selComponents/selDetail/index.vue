<!--详情展示-->

<template>
  <section class="detail-wrapper">
    <SelTitle v-if="title" :title="title"></SelTitle>
    <!-- 编辑 -->
    <SvgIcon
      v-if="showEdit"
      v-hasPermi="hasPermi ? [hasPermi] : undefined"
      :size="24"
      icon-class="edit"
      class="svg-color edit-svg"
      @click="clickEdit"
    />
    <section class="detail-conten-box">
      <el-row :gutter="gutter" style="align-items: flex-end">
        <slot>
          <el-col
            v-for="(item, index) in itemsShow"
            :key="index"
            :offset="item.offset"
            :span="item.span || span"
          >
            <!-- 替换整个el-form-item的插槽 -->
            <template v-if="item.noLabelSlotName">
              <slot :name="item.noLabelSlotName"></slot>
            </template>

            <div v-else class="detail-item">
              <p class="detail-label">{{ item.label }}</p>
              <!-- 包含lable的插槽 -->
              <template v-if="item.slotName">
                <div class="detail-value">
                  <slot :name="item.slotName"></slot>
                </div>
              </template>
              <template v-else-if="item.isLevel">
                <p class="detail-value">
                  <el-tag :type="LEVEL_DATA[detailData[item.prop]]">{{
                    detailData[item.propName]
                  }}</el-tag>
                </p>
              </template>
              <template v-else-if="item.formatter">
                {{ item.formatter(detailData) }}
              </template>
              <template v-else>
                <!-- 跳转到 单位/事件 详情页面 -->
                <div v-if="!item.html" class="detail-value">
                  <div
                    v-if="item.prop == 'deptName' || item.prop == 'eventTitle'"
                    v-clickDept="[item.prop, props.detail]"
                  >
                    跳转 {{ detailData[item.prop as string] || "-" }}
                  </div>
                  <p v-else>1{{ detailData[item.prop as string] || "-" }}</p>
                </div>

                <div
                  v-else
                  v-html="$replaceToken(detailData[item.prop as string] || '-')"
                ></div>
              </template>
            </div>
          </el-col>
        </slot>
      </el-row>
    </section>
  </section>
</template>

<script setup lang="ts">
import type { IStringObject, DetailItem } from "@/config/types";
import { LEVEL_DATA } from "@/config/constant";

interface Props {
  title?: string; //标题
  items?: DetailItem[]; //表单项列表
  detail?: IStringObject; //数据
  gutter?: number; //布局间隔
  span?: number; //默认每行几个表单项
  showEdit?: boolean; //显示编辑按钮
  hasPermi?: string; //编辑按钮的权限标识
}
const props = withDefaults(defineProps<Props>(), {
  detail() {
    return {};
  },
  items() {
    return [];
  },
  gutter: 45,
  span: 6,
  showEdit: true,
  title: "",
  hasPermi: "",
});
const detailData = computed(() => {
  console.log(" props.detail: ", props.detail);
  return props.detail;
});
const emit = defineEmits<{
  (e: "clickEdit"): void;
}>();

const itemsShow = computed(() => {
  return props.items.filter((item) => !item.hide);
});

function clickEdit() {
  emit("clickEdit");
}
</script>
