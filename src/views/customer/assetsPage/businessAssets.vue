<!---->

<template>
  <Component
    :is="currentRouteName == 'AssetsManage' ? ResizeWrapper : 'div'"
    :class="currentRouteName == 'AssetsManage' ? 'tree-table-wrapper' : ''"
  >
    <template #tree>
      <TreeCollection
        :types="[leftTreeType]"
        :spare1="4"
        @getId="getId"
      ></TreeCollection>
    </template>

    <SelTable
      ref="tableRef"
      :btn-list="isManage ? btnList : undefined"
      :columns="columns"
      :load-data="
        isRelevancy && idList
          ? getGetBusinessAssetByParam
          : getGetAssetBusinessList
      "
      :default-params="{ ...defaultParams, _abort: 1 }"
      :search-option="searchOption"
      row-key="id"
      :checkbox="isGrade"
    >
      <template #netIps="{ row }">
        {{ getIpPort(row.netIps) }}
      </template>
      <template #lanIps="{ row }">
        {{ getIpPort(row.lanIps) }}
      </template>
    </SelTable>
  </Component>
</template>

<script setup lang="ts">
import type {
  IStringObject,
  ColumnItem,
  Btn,
  SearchProps,
} from "@/config/types";
import {
  getGetAssetBusinessList,
  postRemove,
} from "@/api/customer/assets/business";
import { getGetBusinessAssetByParam } from "@/api/workSpace/asset";
import { postVulnAssetAssestGroupList } from "@/api/assets/group";
import getIpPort from "@/views/assets/composition/getIpPort";

import useRelevancyAsset from "@/views/event/composition/relevancyAsset";
import useSelectAsset from "./composition/selectAsset";

import ResizeWrapper from "@/globalComponents/resizeWrapper.vue";
import useUser from "@/store/modules/user";
const leftTreeType = computed(() => {
  return useUser().leftTreeType;
});

/**关联资产使用 props和emit*/
interface Props {
  isManage?: boolean; ////是否是资产管理页面 -资产台账 有增删改操作按钮
  isRelevancy?: boolean; //是否是关联资产引用
  eventId?: string; //事件id,详情内的关联资产
  isCoProcess?: boolean; //协同处置-资产
  idList?: string; //新增事件 -关联资产
  isGrade?: boolean; //新增编辑等保
  deptId?: string; //等保 传入的单位id
}
const props = withDefaults(defineProps<Props>(), {
  isManage: false,
  isRelevancy: false, //是否是关联资产
  eventId: "", //事件id
  isCoProcess: false,
  idList: "",
  isGrade: false,
  deptId: "",
});

//打开关联资产
const emit = defineEmits<{
  (e: "openAssociateReason", id: number): void; //点击操作列关联按钮
  (e: "select", row: IStringObject): void; //协同处置点击操作列确定
}>();

/**关联资产 */
const { relevancyColumn, relevancyBtn, relevancyParams } = useRelevancyAsset(
  props,
  emit
);

/**协同处置-资产 */
const { selectBtn } = useSelectAsset(emit);

const router = useRouter();
const route = useRoute();

const currentRouteName = route.name;

const id = route.params.id;

const activeMenu =
  currentRouteName == "AssetsManage" ? "/assetsManage" : "/customer/list";

//查询默认参数
const defaultParams = computed(() => {
  //协同处置
  if (props.isCoProcess) {
    return {};
  } else if (props.isGrade) {
    //等保
    return { deptId: props.deptId, notIds: props.idList };
  } else if (props.isRelevancy) {
    //关联资产
    if (props.idList) {
      //新增事件
      return { idList: props.idList, queryStatus: "2" };
    } else {
      return relevancyParams;
    }
  } else if (props.isManage) {
    //聚合资产
    if (currentRouteName == "AssetsManage") {
      return treeParams.value;
    }
    //资产台账
    return { params: { deptTreeId: id, deptTreeType: 2 } };
  }
});

const tableRef = ref<any>(null);

//查询配置
const searchOption = ref<SearchProps>({
  // showFilter: props.isManage ? true : false,
  modelValue: {
    assetName: "",
    url: "",
    ipStr: "",
    port: "",
    protocolType: "",
    isOwn: "",
    pageNum: "",
    pageSize: "",
    groupId: "",
  },
  items: [
    {
      prop: "assetName",
      label: "资产名称",
      is: "el-input",
    },
    {
      prop: "url",
      label: "业务入口",
      is: "el-input",
    },
    {
      prop: "ipStr",
      label: "IP",
      is: "el-input",
    },
    {
      prop: "port",
      label: "端口",
      is: "el-input",
    },
    {
      prop: "protocolType",
      label: "服务协议",
      is: "el-select",
      option: {
        dictName: "service_agreement",
      },
    },
    {
      prop: "groupId",
      label: "资产组",
      is: "el-select",
      option: {
        getList: {
          api: postVulnAssetAssestGroupList,
          resKey: "rows",
          valueKey: "id",
          labelKey: "name",
          params: {
            pageSize: 0,
            deptId: props.deptId,
          },
        },
      },
    },
  ],
  menuData: [{ label: "是否为有主资产", prop: "isOwn", dictName: "whether" }],
});
//表格上方按钮
const btnList = ref<Btn[]>([
  {
    icon: "add",
    title: "新增",
    hasPermi: "vulnAsset:business:add",
    onClick() {
      let deptId = id;
      if (currentRouteName == "AssetsManage") {
        if (
          treeParams.value.params &&
          treeParams.value.params.deptTreeType == 2
        ) {
          deptId = treeParams.value.params.deptTreeId;
        }
      }
      router.push({
        name: "AddAsset",
        params: {
          type: "product",
        },
        query: {
          deptId,

          activeMenu,
        },
      });
    },
  },
  {
    icon: "import",
    hasPermi: "vulnAsset:business:add",
    option: {
      exportUrl: "/vulnAsset/business/downloadTemplate",
      importUrl: "/vulnAsset/business/importData",
      showUpdateSupport: false,
    },
    title: "导入",
  },
  {
    icon: "export",
    hasPermi: "vulnAsset:business:export",
    option: {
      exportUrl: "/vulnAsset/business/exportAsset",
      params: searchOption.value.modelValue,
      getParams() {
        //聚合资产
        if (currentRouteName == "AssetsManage") {
          return treeParams.value;
        }
        //资产台账
        return { params: { deptTreeId: id, deptTreeType: 2 } };
      },
    },
    title: "导出",
  },
]);

//表格columns
const columns = ref<ColumnItem[]>([
  {
    prop: "assetName",
    label: "资产名称1",
    onClick(scope: any) {
      router.push({
        name: "DetailAsset",
        params: { id: scope.row.id, type: "product" },
        query: {
          activeMenu,
        },
      });
    },
    hide: !props.isManage,
  },
  {
    prop: "assetNo",
    label: "资产编号",
  },
  {
    prop: "assetTypeName",
    label: "资产类型",
  },
  {
    prop: "url",
    label: "业务入口",
  },
  {
    prop: "netIps",
    label: "对外服务IP/端口",
    slotName: "netIps",
  },
  {
    prop: "lanIps",
    label: "计算设备IP/端口",

    slotName: "lanIps",
  },
  {
    prop: "deptName",
    label: "责任单位",
    hide: currentRouteName !== "AssetsManage",
  },
  {
    prop: "deploymentAddress",
    label: "部署地址",
    hide: true,
  },
  {
    slotName: "action",
    btnList: [
      {
        icon: "edit",
        title: "编辑",
        onClick({ row }) {
          router.push({
            name: "EditAsset",
            params: {
              id: row.id,
              type: "product",
            },
            query: {
              activeMenu,
            },
          });
        },
      },
      {
        icon: "delete",
        title: "删除",
        hasPermi: "vulnAsset:business:delete",
        onClick(scoped) {
          postRemove({ ids: [scoped.row.id], _msg: 1 }).then(() => {
            tableRef.value?.search();
          });
        },
      },
    ],
    hide: !props.isManage,
  },
]);

if (!props.isManage) {
  columns.value.unshift(...relevancyColumn);
}
/**关联资产 */
if (props.isRelevancy) {
  columns.value.push(relevancyBtn);
}
/**资产协同 */

if (props.isCoProcess) {
  columns.value.push(selectBtn);
}

//聚合页面的单位树
const treeParams: IStringObject = ref({});
function getId(id: string, type: string, params: IStringObject) {
  treeParams.value = { ...params };
  nextTick(() => {
    tableRef.value?.search();
  });
}

defineExpose({
  search: () => tableRef.value?.search(),
  tableRef: computed(() => tableRef.value),
});
</script>

<style scoped lang="scss"></style>
