<template>
  <div class="notice-channel-new">
    <SelTitle title="联系人通知渠道配置" style="margin-bottom: 20px">
    </SelTitle>
    <UserNotice :dept-id="deptId"></UserNotice>

    <SelTitle
      title="群通知渠道配置"
      style="margin-bottom: 20px; margin-top: 20px"
    >
    </SelTitle>
    <GroupNoticeChannel ref="groupList" :dept-id="deptId"></GroupNoticeChannel>

    <SelTitle title="企业微信群配置" style="margin-bottom: 20px"> </SelTitle>
    <QywxGroup :dept-id="deptId"></QywxGroup>

    <SelTitle title="飞书群配置" style="margin-bottom: 20px"> </SelTitle>
    <FsGroup :dept-id="deptId" @resetGroupList="searchGroup"></FsGroup>

    <SelTitle title="微信混合群配置" style="margin-bottom: 20px"> </SelTitle>
    <NoticeWX :dept-id="deptId" @resetGroupList="searchGroup"></NoticeWX>
  </div>
</template>
<script setup lang="ts">
import NoticeWX from "../noticeChannelPage/noticeWX.vue";
import UserNotice from "../noticeChannelPage/userNotice.vue";
import GroupNoticeChannel from "../noticeChannelPage/groupNoticeChannel.vue";
import QywxGroup from "../noticeChannelPage/qywxGroup.vue";
import FsGroup from "../noticeChannelPage/fsGroup.vue";
interface Props {
  deptId?: string;
}
withDefaults(defineProps<Props>(), {
  deptId: "public_dept_id",
});
const groupList = ref<any>(null);
function searchGroup() {
  groupList?.value.getTableList();
}
</script>
<style scoped lang="scss">
.mt20 {
  margin-top: 20px;
}
</style>
