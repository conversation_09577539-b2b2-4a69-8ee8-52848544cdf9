<template>
  <el-tabs v-model="activeName" class="webVuln-tab-new">
    <el-tab-pane
      name="settings"
      :lazy="true"
      :label="route.name != 'CommonOperation' ? '分析师配置' : '运营设置'"
    >
      <div
        v-for="menuItem in menuList"
        :key="menuItem.title"
        class="detail-wrapper"
        :class="menuItem.className"
      >
        <div v-show="!menuItem?.ifShow">
          <SelTitle
            v-if="menuItem.hasPermi"
            v-hasPermi="menuItem.hasPermi"
            :title="menuItem.title"
            class="sel-title"
            :remark="menuItem.remark"
          >
          </SelTitle>
          <SelTitle
            v-else
            :title="menuItem.title"
            class="sel-title"
            :remark="menuItem.remark"
          ></SelTitle>
          <p
            v-if="menuItem.type == 2"
            v-hasPermi="'alert:rule:edit'"
            class="switch-connection"
          >
            是否关联日志匹配规则
            <el-switch
              v-model="alertRuleMerge"
              :active-value="1"
              :inactive-value="0"
              @change="changeAlertRuleMerge"
            ></el-switch>
          </p>
        </div>
        <Component
          :is="menuItem.content"
          v-if="'type' in menuItem ? keyValueMap : true"
          :type="menuItem.type"
          :key-value-map="keyValueMap"
          :key-status="logRuleUpdateStatus"
          :alert-rule-merge="alertRuleMerge"
          @successSetAlarmRule="successSetAlarmRuleFn"
        />
      </div>
    </el-tab-pane>
    <el-tab-pane
      v-if="route.name == 'CommonOperation'"
      :lazy="true"
      name="alarmRules"
      label="告警归属匹配规则"
    >
      <AlarmRules v-hasPermi="['alert:rule:list']" />
    </el-tab-pane>
  </el-tabs>
</template>
<script setup lang="ts">
import AlarmRules from "../setOperation/alarmRules.vue";
import WebVuln from "../setOperation/webVuln.vue";
// import IntranetMonitor from "../setOperation/intranetMonitor.vue";
import Analyst from "../setOperation/analyst.vue";
import Linkman from "../setOperation/linkman.vue";
import AlertEvent from "../setOperation/alertEvent.vue";
import OperateParam from "../setOperation/operateParam.vue";
import EngineVuln from "../setOperation/engineVuln.vue";
import ServiceItemManagement from "../setOperation/ServiceItemManagement.vue";
import Availability from "@/views/customer/usability/availability.vue";
import AssetEgineConfig from "@/views/customer/usability/assetEgineConfig.vue";
import {
  switchAlertRuleMerge,
  getSiemRuleNames,
} from "@/api/customer/setOperation/alertEvent";
import { ElMessageBoxFun } from "@/utils/elMessageBox";

const props = defineProps<{
  deptData?: any;
}>();
const route = useRoute();
interface Menu {
  className?: string;
  title: string;
  type?: number;
  content: any;
  hasPermi?: any;
  ifShow?: boolean;
  remark?: string;
}
const activeName = ref("settings");
// 所有的tabs label:tab名称, content:组件内容，hasPermi:权限
const menuList = ref<Menu[]>([]);
if (route.name == "CommonOperation") {
  menuList.value.push(
    {
      className: "Availability",
      title: "可用性监测引擎配置",
      content: Availability,
    },
    {
      className: "AssetEgineConfig",
      title: "资产变更监测引擎配置",
      content: AssetEgineConfig,
    },
    {
      className: "WebVuln",
      title: "应用漏洞监测配置",
      content: WebVuln,
    },
    {
      className: "vuln",
      title: "主机漏洞监测引擎配置",
      content: EngineVuln,
    },

    {
      className: "analyst",
      title: "分析师配置",
      content: Analyst,
    },

    {
      className: "OperateParam",
      title: "运营参数",
      content: OperateParam,
    }
  );
} else {
  menuList.value.push(
    // {
    //   className: "WebVuln",
    //   title: "应用漏洞监测配置",
    //   content: WebVuln,
    //   ifShow: true, //运营客户详情时不显示标题，tab代替
    // },
    // {
    //   className: "vuln",
    //   title: "主机漏洞监测引擎配置",
    //   content: EngineVuln,
    // },
    // {
    //   className: "assets",
    //   title: "内网监测IP配置",
    //   content: IntranetMonitor,
    // },
    {
      className: "analyst",
      title: "分析师配置",
      content: Analyst,
      ifShow: true,
    },
    {
      className: "Linkman",
      title: "信息接收人配置",
      content: Linkman,
    },
    {
      className: "AlertEvent",
      title: "告警归属匹配规则",
      type: 2,
      content: AlertEvent,
      remark: "以下所有告警规则都以告警匹配规则结果为基准",
    },
    {
      className: "AlertEvent",
      title: "日志归属匹配规则",
      type: 4,
      content: AlertEvent,
    },
    {
      className: "AlertEvent",
      title: "告警过滤规则",
      type: 1,
      content: AlertEvent,
    },
    // 类型：0-紧急告警；1-告警过滤 ;2-告警匹配规则3.告警通知规则 4.日志归属匹配规则
    {
      className: "AlertEvent",
      title: "非工作时间告警通知规则",
      type: 0,
      content: AlertEvent,
    },

    {
      className: "AlertEvent",
      title: "特殊告警通知规则",
      type: 3,
      content: AlertEvent,
    },

    {
      className: "OperateParam",
      title: "运营参数",
      content: OperateParam,
    },
    {
      className: "OperateParam",
      title: "服务项管理",
      content: ServiceItemManagement,
      hasPermi: ["system:serviceitem:list", " system:deptServiceitem:list"],
    }
  );
}

const alertRuleMerge = ref(0); // 是否关联日志匹配规则
const alertRuleMergeWatcher = watch(
  () => props.deptData,
  () => {
    if (props.deptData) {
      alertRuleMerge.value = props.deptData.alertRuleMerge;
      nextTick(() => {
        alertRuleMergeWatcher();
      });
    }
  },
  {
    immediate: true,
  }
);

const logRuleUpdateStatus = ref(true); // 日志规则是否更新，用于同步按钮打开后刷新页面
function changeAlertRuleMerge(val: number) {
  if (val == 1) {
    ElMessageBoxFun("打开操作会覆盖现有日志的配置，确定打开吗")
      .then(() => {
        switchAlertRuleMergeFn();
      })
      .catch(() => {
        alertRuleMerge.value = 0;
      });
  } else {
    switchAlertRuleMergeFn();
  }
}

function switchAlertRuleMergeFn() {
  const params = {
    deptId: props.deptData.deptId,
    alertRuleMerge: alertRuleMerge.value,
    _msg: 1,
  };
  switchAlertRuleMerge(params)
    .then(() => {
      successSetAlarmRuleFn();
    })
    .catch(() => {
      alertRuleMerge.value = 0;
    });
}

// 告警规则更新成功后，如果开启了同步，刷新日志规则列表
function successSetAlarmRuleFn() {
  if (alertRuleMerge.value) {
    logRuleUpdateStatus.value = !logRuleUpdateStatus.value;
  }
}

getSiemRuleNamesFn();
const keyValueMap = ref<null | { [key: string]: string }>(null);
function getSiemRuleNamesFn() {
  getSiemRuleNames().then((res: any) => {
    keyValueMap.value = res;
  });
}
</script>

<style lang="scss" scoped>
.sel-title {
  margin-bottom: 20px;
}
.switch-connection {
  position: absolute;
  right: 20px;
  transform: translateY(-110%);
}
.app-3 {
  .analyst {
    margin-top: 15px;
  }
}
</style>
