<template>
  <div v-if="showBaseDetail" class="baseInfo-wrapper">
    <SelDetail
      title="单位信息"
      :detail="deptData"
      :items="detailItems"
      has-permi="system:responsibility:dept:update"
      @clickEdit="openEditBase"
    >
      <template #region>
        <span v-if="deptData.provinceName">
          {{ deptData.provinceName }}
          {{ deptData.cityName }}
          {{ deptData.countyName }}
        </span>
        <span v-else>-</span>
      </template>
      <template #isClassifiedProtection>
        {{ deptData.isClassifiedProtection ? "是" : "否" }}
      </template>
    </SelDetail>
  </div>
  <div
    v-show="true"
    v-hasPermi="['system:dept:query']"
    class="baseInfo-wrapper m20"
  >
    <SelTitle
      title="实时状态"
      class="sel-title"
      style="margin-bottom: 20px"
    ></SelTitle>
    <el-row :gutter="20">
      <!-- 已发现安全漏洞 -->
      <el-col :span="8">
        <el-card class="safe-vuln-box">
          <template #header
            ><span class="card-title">已发现安全漏洞</span></template
          >
          <div v-loading="safeVulnLoading" class="sameEchart body-height">
            <div id="safeVulnEchart" style="height: 230px; width: 75%"></div>
            <div class="vuln-value-rate">
              <template v-for="(item, index) in safeVulnList" :key="index">
                <p>
                  <span>{{ item.rate }}</span>
                  <span>{{ item.value }}</span>
                </p>
              </template>
            </div>
          </div>
        </el-card>
      </el-col>
      <!--  已分析安全事件 -->
      <!-- 年度事件总量 -->
      <el-col :span="16">
        <el-card class="event-box">
          <div class="event-body event-height">
            <div>
              <div class="event-time">
                <span class="card-title"> 已分析安全事件</span>
                <p class="btn-list">
                  <span
                    :class="choseEventNum == '4' ? 'chose-btn' : ''"
                    @click="changeEventTimeScope('4')"
                    >周</span
                  >
                  <span
                    :class="choseEventNum == '5' ? 'chose-btn' : ''"
                    @click="changeEventTimeScope('5')"
                    >月</span
                  >
                  <span
                    :class="choseEventNum == '7' ? 'chose-btn' : ''"
                    @click="changeEventTimeScope('7')"
                    >年</span
                  >
                </p>
                <div class="clearfix"></div>
              </div>
              <div
                id="eventTypeEchart"
                v-loading="eventTypeLoading"
                :class="{ 'no-data': !eventPieShowStatus }"
                style="height: 240px"
              ></div>
            </div>
            <div>
              <div class="event-count">
                <div class="count">
                  <p>{{ eventCount.yearEventCount }}</p>
                  <p>年度事件总量</p>
                </div>
                <div class="count">
                  <p>{{ eventCount.yearEventHandleRate }}</p>
                  <p>年度事件处置率</p>
                </div>
                <div class="count">
                  <p>{{ eventCount.monthEventAddCount }}</p>
                  <p>本月新增事件</p>
                </div>
                <div class="count">
                  <p>{{ eventCount.eventNotHandleCount }}</p>
                  <p>未处置完毕事件</p>
                </div>
              </div>
              <div
                id="eventStatisEchart"
                v-loading="eventStatisLoading"
                style="height: 220px"
              ></div>
            </div>
          </div>
        </el-card>
      </el-col>
      <!-- 资产数统计 -->
      <el-col :span="8" class="mt20">
        <el-card>
          <div class="body-height asset-count">
            <div class="count">
              <p>{{ assetCountNum.assetTotalRiskCount }}</p>
              <p>已发现高危资产</p>
            </div>
            <div class="count">
              <p>{{ assetCountNum.assetBusinessCount }}</p>

              <p>业务系统资产</p>
            </div>
            <div class="count">
              <p>{{ assetCountNum.assetResourceCount }}</p>

              <p>计算设备资产</p>
            </div>
            <!--  -->
            <!-- <div class="line"></div>
            <div class="count">
              <p>51</p>
              <p>已提交报告</p>
            </div> -->
          </div>
        </el-card>
      </el-col>
      <!-- 告警类型占比 -->
      <!-- 已处理告警 -->
      <el-col :span="16" class="mt20">
        <el-card>
          <div class="body-height event-body">
            <div>
              <div>
                <span class="card-title">告警类型占比</span>
                <p class="btn-list">
                  <span
                    :class="choseAlertNum == '4' ? 'chose-btn' : ''"
                    @click="changeAlertTimeScope('4')"
                    >周</span
                  >
                  <span
                    :class="choseAlertNum == '5' ? 'chose-btn' : ''"
                    @click="changeAlertTimeScope('5')"
                    >月</span
                  >
                  <span
                    :class="choseAlertNum == '7' ? 'chose-btn' : ''"
                    @click="changeAlertTimeScope('7')"
                    >年</span
                  >
                </p>
                <div class="clearfix"></div>
              </div>
              <div
                id="alertTypeEchart"
                v-loading="alertTypeLoading"
                style="height: 240px"
                :class="{ 'no-data': !warningPieShowStatus }"
              ></div>
            </div>
            <div>
              <p class="card-title">已处理告警</p>
              <div v-loading="alertLevelLoading" class="alertLevel">
                <div
                  id="alertLevelEchart"
                  style="height: 240px; width: 80%"
                ></div>
                <div class="waning-list">
                  <template
                    v-for="(item, index) in alertLevelList"
                    :key="index"
                  >
                    <p>
                      <span>{{ item.rate }}</span>
                      <span>{{ item.value }}</span>
                    </p>
                  </template>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
  <div v-if="usePermissionFlag('system:dept:query')">
    <SelTitle title="业务系统资产信息" class="sel-title"></SelTitle>
    <AssetInfo v-if="showBaseDetail" :type="1" :num-obj="numBusiness" />
  </div>
  <div v-if="usePermissionFlag('system:dept:query')">
    <SelTitle title="计算设备资产信息" class="sel-title"></SelTitle>
    <AssetInfo v-if="showBaseDetail" :type="2" :num-obj="numComputed" />
  </div>
</template>

<script setup lang="ts">
import { debounce, cloneDeep } from "lodash";
import AssetInfo from "../components/assetInfo.vue";
import type { IStringObject, DetailItem } from "@/config/types";
import { getGetSecurityVuln } from "@/api/customer/vuln/computed";
import usePermissionFlag from "@/utils/permissionFlag";

// 已分析安全事件统计、年度事件总量、事件处置率等
import {
  selectEventAnalysisCount,
  getEventStatisData,
  selectEventStatisList,
} from "@/api/event/eventList";
import {
  getGetAlertTypeCount,
  getGetAlertDealCount,
} from "@/api/customer/alert";
import { getOperatingDeptAssetCount } from "@/api/customer/baseDetail";
import * as echarts from "echarts";
import { markRaw } from "vue";

interface Props {
  deptData?: IStringObject;
}

withDefaults(defineProps<Props>(), {
  deptData: () => {
    return {};
  },
});

const showBaseDetail = ref(true);

const router = useRouter();
const route = useRoute();
const windowWidth = ref(0);
function getWinWidthFun() {
  if (route.name === "CustomerDetail") {
    windowWidth.value = window.innerWidth;
  }
}
onMounted(() => {
  window.addEventListener(
    "resize",
    debounce(() => {
      getWinWidthFun();
    })
  );
});
onBeforeUnmount(() => {
  window.removeEventListener("resize", getWinWidthFun);
});

// 监听
/* watch(
  () => windowWidth.value,
  (val) => {
    windowWidth.value = val;
    const option = cloneDeep(optionsBar.value);
    if (val > 1530) {
      option.series[0].emphasis.label.fontSize = 14; //饼图中间的文字size
      option.legend.textStyle.width = 150; //饼图右侧文字宽度
    } else {
      option.series[0].emphasis.label.fontSize = 12; //饼图中间的文字size
      option.legend.textStyle.width = 90; //饼图右侧文字宽度
    }

    if (eventTypeEchart.value && alertTypeEchart.value) {
      eventTypeEchart.value.setOption(option);
      alertTypeEchart.value.setOption(option);
    }
  }
); */

watch(
  () => route.name,
  (val) => {
    if (val === "CustomerDetail") {
      showBaseDetail.value = true;
      nextTick(() => {
        getPageData();
      });
    } else {
      showBaseDetail.value = false;
    }
  }
);

const detailItems = ref<DetailItem[]>([
  {
    prop: "deptName",
    label: "单位名称",
  },
  {
    prop: "shortName",
    label: "单位简称",
  },
  {
    prop: "deptTypeName",
    label: "单位类型",
  },
  {
    prop: "industryTypeName",
    label: "行业类型",
  },
  {
    prop: "address",
    label: "单位地址",
  },
  {
    prop: "postCode",
    label: "邮政编码",
  },
  {
    prop: "region",
    label: "行政区划",
    slotName: "region",
  },
  {
    prop: "regionNumber",
    label: "行政区划代码",
  },
  {
    prop: "parentDeptName",
    label: "所属主管部门",
  },
  {
    prop: "isClassifiedProtection",
    label: "是否等保备案单位",
    slotName: "isClassifiedProtection",
  },

  {
    prop: "longitude",
    label: "单位经度",
  },
  {
    prop: "latitude",
    label: "单位纬度",
  },
]);
function openEditBase() {
  router.push({
    name: "EditSecurity",
    params: { id: route.params.id },
  });
}
// 实时状况
// 已发现安全漏洞
const safeVulnLoading = ref(false);
const safeVulnList = ref([
  {
    value: "0",
    rate: "0",
  },
  {
    value: "0",
    rate: "0",
  },
  {
    value: "0",
    rate: "0",
  },
  {
    value: "0",
    rate: "0",
  },
]);
const safeVulnEchart = ref<IStringObject>({});
const safeVulnOption = ref<any>({
  tooltip: {
    trigger: "item",
    formatter: function ({ data }: any) {
      return `${data.name}: ${data.value} (${data.rate}) `;
    },
  },
  yAxis: {
    type: "category",
    data: [],
    //坐标轴的文字颜色为rgba(255,255,255,.6)  文字大小为12
    axisLabel: {
      color: "#000",
    },
    //坐标轴上的刻度线不显示
    axisTick: {
      show: false,
    },
    //坐标轴线颜色为灰色
    axisLine: {
      lineStyle: {
        color: "#999",
      },
    },
    splitLine: {
      show: false,
    },
  },
  xAxis: {
    type: "value",
    splitLine: {
      show: false,
    },
  },
  grid: {
    top: 30,
    bottom: 30,
    left: "15%",
  },
  legend: {
    width: window.innerWidth < 1530 ? "70%" : "80%",
  },
  series: [
    {
      data: [],
      type: "bar",
      showBackground: true,
      barWidth: 15,

      backgroundStyle: {
        color: "rgba(180, 180, 180, 0.2)",
      },
      itemStyle: {
        normal: {
          color: function (params: any) {
            const colorList = [
              "#df1f1e",
              "#ffce32",
              "#1445b9",
              "#35870d",
              "#bababa",
            ];
            return colorList[params.dataIndex];
          },
        },
      },
    },
  ],
});
function getSecurityVuln() {
  safeVulnLoading.value = true;
  getGetSecurityVuln({ deptId: route.params.id }).then((res: any) => {
    if (res.length > 0) {
      const dataValue = res.map((ite: any) => {
        return {
          ...ite,
          name: ite.levelName,
          value: ite.count ? ite.count : 0,
          rate: (ite.rate ? ite.rate : "0") + "%",
        };
      });
      safeVulnList.value =
        dataValue.length > 0
          ? cloneDeep(dataValue).reverse()
          : safeVulnList.value;

      //避免数字很小时出现小数

      const options = cloneDeep(safeVulnOption.value);

      const maxVal = Math.max(...dataValue.map((item) => item.value || 0));
      const interval = maxVal <= 5 ? 1 : null; // 根据最大值动态设置间隔
      options.xAxis.interval = interval;
      options.series[0].data = dataValue;
      options.yAxis.data = dataValue.map((item) => item.name);
      if (safeVulnEchart.value && safeVulnEchart.value.dispose) {
        safeVulnEchart.value.dispose();
      }
      safeVulnEchart.value = markRaw(
        echarts.init(document.getElementById("safeVulnEchart") as any)
      );
      safeVulnEchart.value.setOption(options);
      safeVulnLoading.value = false;
    }
  });
}

/* 已分析安全事件 */
const choseEventNum = ref("4");
const eventTypeEchart = ref<IStringObject>({});
const optionsBar = ref<IStringObject>({
  tooltip: {
    trigger: "item",
    formatter: " {c} ({d}%)",
    textStyle: {
      fontSize: 12,
    },
  },
  title: {
    text: "事件类型分布",
    show: true,
    textStyle: {
      fontSize: 12,
      color: "#aaa",
      textAlign: "center",
    },
    bottom: "5%",
    left:
      window.innerWidth == 1920
        ? "21%"
        : window.innerWidth >= 1510
        ? "19%"
        : "15%",
  },

  legend: {
    left: "60%",
    bottom: "center",
    orient: "vertical",
    height: "100%",
    type: "scroll",
    icon: "circle",
    formatter: function (title: string) {
      const data = optionsBar.value.series[0].data;
      let proportion = 0;
      let count = 0;
      for (let i = 0; i < data.length; i++) {
        if (data[i].name == title) {
          proportion = data[i].rate;
          count = data[i].value;
        }
      }

      return `${title}  ${proportion}%  ${count}`;
    },
    textStyle: {
      width: window.innerWidth < 1530 ? 90 : 150, //饼图右侧文字宽度
      overflow: "truncate",
    },
  },
  xAxis: {
    show: false,
  },
  yAxis: {
    show: false,
  },
  series: [
    {
      type: "pie",
      radius: ["53%", "70%"],
      center: ["30%", "45%"],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: "center",
      },
      emphasis: {
        label: {
          show: true,
          fontSize: window.innerWidth < 1530 ? 12 : 16, //饼图中间的文字size
          fontWeight: "bold",
          formatter: function (params: any) {
            return formatPieTitle(params.name);
          },
        },
      },
      labelLine: {
        show: false,
      },
      data: [],
      itemStyle: {
        borderColor: "white",
        borderWidth: 2, // 设置边框宽度
      },
    },
  ],
});
// 切换查询事件内容范围
const eventPieShowStatus = ref(true);
const eventTypeLoading = ref(false);
function changeEventTimeScope(time: string) {
  eventTypeLoading.value = true;
  choseEventNum.value = time;
  selectEventAnalysisCount({
    deptId: route.params.id,
    timeState: time,
  }).then((res) => {
    const option = cloneDeep(optionsBar.value);
    const data = res.data;
    // console.log("已分析安全事件", res, obj);
    if (data.length > 0) {
      data.forEach((el: any) => {
        el.value = el.count;
        el.name = el.title;
      });
    }
    eventPieShowStatus.value = data.length > 0;
    option.series[0].data = data;

    if (eventTypeEchart.value.dispose) {
      eventTypeEchart.value.dispose();
    }
    eventTypeEchart.value = markRaw(
      echarts.init(document.getElementById("eventTypeEchart") as any) //初始化
    );
    eventTypeEchart.value.setOption(option); //更新表格
    eventTypeLoading.value = false;
  });
}

/* 事件总量、事件处置率*/
const eventCount = ref<IStringObject>({});
function getEventCount() {
  getEventStatisData({ deptId: route.params.id }).then((res: any) => {
    eventCount.value = res.data;
  });
}
/*事件处置柱状图*/

const _style = getComputedStyle(document.body);
const colorSub = _style.getPropertyValue("--primary-color-sub").trim();
const colorSub3 = _style.getPropertyValue("--primary-color-sub-3").trim();
const eventStatisEchart = ref<IStringObject>({});
const eventStatisEchartOption = ref<IStringObject>({
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "shadow",
    },
  },
  dataset: {
    source: [],
  },
  grid: {
    left: "3%",
    right: "4%",
    bottom: "6%",
    top: "3%",
    containLabel: true,
  },
  yAxis: {
    type: "value",
    minInterval: 1,
    splitLine: {
      show: true,
    },
  },
  xAxis: {
    type: "category",
    splitLine: {
      show: true,
    },
  },
  series: [
    {
      type: "bar",
      barWidth: "14px",
      itemStyle: {
        color: colorSub3,
      },
    },
    {
      type: "bar",
      barWidth: "14px",
      itemStyle: {
        color: colorSub,
      },
    },
  ],
});
const eventStatisLoading = ref(false);
function getEventStatisEchartData() {
  eventStatisLoading.value = true;
  selectEventStatisList({ deptId: route.params.id }).then((res) => {
    const arr: any = [];
    if (res.data) {
      res.data.forEach((item: any) => {
        arr.unshift([item.time, item.count, item.notHandleCount]);
      });
      arr.unshift(["product", "当月事件总量", "未处置事件"]);
      eventStatisEchartOption.value.dataset.source = arr;
      if (eventStatisEchart.value.dispose) {
        eventStatisEchart.value.dispose();
      }
      eventStatisEchart.value = markRaw(
        echarts.init(document.getElementById("eventStatisEchart") as any)
      );
      eventStatisEchart.value.setOption(eventStatisEchartOption.value);
      eventStatisLoading.value = false;
    }
  });
}
/* 告警内容*/
// 告警类型占比
const choseAlertNum = ref("4");
const warningPieShowStatus = ref(true);
const alertTypeLoading = ref(false);
const alertTypeEchart = ref<IStringObject>({});
function changeAlertTimeScope(time: string) {
  alertTypeLoading.value = true;
  // const obj = { deptId: route.params.id, timeState: time };
  getGetAlertTypeCount({ deptId: route.params.id, timeState: time }).then(
    (res: any) => {
      const option = cloneDeep(optionsBar.value);
      choseAlertNum.value = time;
      warningPieShowStatus.value = res.length > 0;
      // console.log("告警类型占比", res, obj);
      option.title.text = "";
      if (res.length > 0) {
        res.forEach((el: any) => {
          el.value = el.count;
          el.name = el.title;
        });
      }
      option.series[0].data = res;
      if (alertTypeEchart.value && alertTypeEchart.value.dispose) {
        alertTypeEchart.value.dispose();
      }
      alertTypeEchart.value = markRaw(
        echarts.init(document.getElementById("alertTypeEchart") as any)
      );
      alertTypeEchart.value.setOption(option);
      alertTypeLoading.value = false;
    }
  );
}
/*已处理告警*/
const alertLevelLoading = ref(false);
const alertLevelList = ref([
  {
    value: "0",
    rate: "0",
  },
  {
    value: "0",
    rate: "0",
  },
  {
    value: "0",
    rate: "0",
  },
  {
    value: "0",
    rate: "0",
  },
  {
    value: "0",
    rate: "0",
  },
]);

const alertLevelEchart = ref<any>(null);
function getAlertDealCount() {
  alertLevelLoading.value = true;
  getGetAlertDealCount({ deptId: route.params.id }).then((res: any) => {
    const options = cloneDeep(safeVulnOption.value);
    let dataValue = [];

    if (res.length > 0) {
      dataValue = res.map((ite: any) => {
        return {
          ...ite,
          name: ite.priorityName,
          value: ite.count ? ite.count : 0,
          rate: (ite.rate ? ite.rate : "0") + "%",
        };
      });
    }
    alertLevelList.value =
      dataValue.length > 0
        ? cloneDeep(dataValue).reverse()
        : alertLevelList.value;
    //避免数字很小时出现小数
    const maxVal = Math.max(...dataValue.map((item) => item.value || 0));
    const interval = maxVal <= 5 ? 1 : null; // 根据最大值动态设置间隔
    options.xAxis.interval = interval;
    options.series[0].data = dataValue;

    options.yAxis.data = dataValue.map((item) => item.name);
    if (alertLevelEchart.value && alertLevelEchart.value.dispose) {
      alertLevelEchart.value.dispose();
    }
    alertLevelEchart.value = markRaw(
      echarts.init(document.getElementById("alertLevelEchart") as any)
    );

    alertLevelEchart.value.setOption(options);
    alertLevelLoading.value = false;
  });
}
//实时状态:资产数统计
const assetCountNum = ref<any>({
  assetTotalRiskCount: 0,
  assetBusinessCount: 0,
  assetResourceCount: 0,
});

// 业务系统资产统计
const numBusiness = ref({ sum: 0, num: 0 });
// 计算设备资产统计
const numComputed = ref({ sum: 0, num: 0 });

function getAssetCount() {
  getOperatingDeptAssetCount({ deptId: route.params.id }).then((res: any) => {
    assetCountNum.value = res;
    numBusiness.value = {
      sum: res.assetBusinessCount, //业务系统总数
      num: res.assetBusinessRiskCount, //业务系统高危资产数
    };
    numComputed.value = {
      sum: res.assetResourceCount, //计算设备总数
      num: res.assetResourceRiskCount, //计算设备高危资产数
    };
  });
}

//设置环形图是否换行
function formatPieTitle(title: string) {
  const maxLength = 13; // 设置每行最大的字符数
  let str = "";
  while (title.length > maxLength) {
    str += title.slice(0, maxLength) + "\n";
    title = title.slice(maxLength);
  }
  str += title;
  return str;
}

onMounted(() => {
  getPageData();
});
function getPageData() {
  if (usePermissionFlag("system:dept:query")) {
    setTimeout(() => {
      getSecurityVuln();
      changeEventTimeScope("4");
      getEventCount();
      getEventStatisEchartData();
      changeAlertTimeScope("4");
      getAlertDealCount();
      getAssetCount();
    }, 200);
  }
}
</script>

<style lang="scss" scoped>
.base-info {
  margin-top: 24px;
  padding: 14px 46px;
  height: 248px;

  width: 100%;
  .item-wrapper {
    flex-direction: end;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    .item {
      width: 50%;
      margin-bottom: 18px;
    }
  }
}
.m20 {
  margin: 20px 0;
}
:deep .el-card__body {
  padding: 0 !important;
}
.sameEchart {
  display: flex;
  & > div {
    &:nth-child(2) {
      margin-top: 50px;
    }
    & > p {
      height: 20px;
      margin-bottom: 35px;
      white-space: nowrap;
      & > span {
        display: inline-block;
        &:nth-child(1) {
          width: 50px;
        }
        // margin-right: 20px;
        font-weight: bold;
      }
    }
  }
}
.body-height {
  height: 300px;
}
.event-height {
  // height: 340px;
}
.event-time {
  // margin-bottom: 20px;
}
.event-body {
  display: flex;
  padding: 14px 10px 10px;
  & > div {
    padding-left: 10px;
    padding-right: 10px;
    max-width: 50%;
    flex: 1;
    // border: 1px solid red;
    &:nth-child(2) {
      border-left: 1px solid #e4e4e4;
    }
  }
}
.btn-list {
  float: right;
  border: 2px solid var(--primary-color-sub-3);
  border-radius: 18px;
  overflow: hidden;
  margin-right: 20px;
  white-space: nowrap;
  transform: scale(0.9);
  & > span {
    display: inline-block;
    height: 25px;
    width: 40px;
    text-align: center;
    line-height: 25px;
    color: var(--primary-color-sub-3);
    background-color: #fff;
    cursor: pointer;
    &.chose-btn {
      background-color: var(--primary-color-sub-3);
      color: #fff;
    }
  }
}
.event-count {
  display: flex;
  justify-content: space-between;
  margin: -2px 0px 8px 16px;
}

.count {
  & > p {
    &:nth-child(1) {
      color: var(--primary-color);
      font-size: 15px;
      line-height: 22px;
      font-weight: 600;
    }
    &:nth-child(2) {
      font-size: 13px;
      font-weight: 400;
      color: #3b3d38;
      line-height: 17px;
      margin-top: 2px;
    }
  }
}
.card-title {
  font-size: 12px;
  font-weight: bold;
  color: #262824;
  line-height: 20px;
}
.alertLevel {
  display: flex;
  & > div {
    &:nth-child(2) {
      margin-top: 45px;
    }
    & > p {
      height: 20px;
      margin-bottom: 25px;
      white-space: nowrap;
      & > span {
        display: inline-block;
        &:nth-child(1) {
          width: 50px;
        }
        margin-right: 20px;
        font-weight: bold;
      }
    }
  }
}
.asset-count {
  display: flex;
  flex-direction: column;
  padding: 50px;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: start;
  & > div {
    // width: 33.3%;
    &:nth-child(4) {
      margin-top: 20px;
    }
  }
}
.line {
  width: 300%;
  height: 1px;
  border: 1px solid #e5e5e5;
  margin-bottom: 41px;
}
.safe-vuln-box {
  :deep(.el-card__header) {
    border-bottom: none;
  }
  .vuln-value-rate {
    margin-top: 44px !important;
    p {
      margin-bottom: 23px !important;
    }
  }
}
.safe-vuln-box,
.event-box {
  height: 294px;
}
.waning-list {
  margin-top: 38px !important;
  p {
    margin-bottom: 17px !important  ;
  }
}
.no-data {
  position: relative;
  &:after {
    content: "暂无数据";
    display: flex;
    justify-content: center;
    align-items: center;
    color: #bbb;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #fff;
  }
}
</style>
