<template>
  <ResizeWrapper ref="resizeRef" class="tree-table-wrapper">
    <!-- 左侧树 -->
    <template #tree>
      <TreeCollection
        ref="treeCollectionRef"
        :types="[leftTreeType]"
        :spare1="4"
        @getId="getId"
      ></TreeCollection>
    </template>
    <section style="overflow-x: hidden" class="customer-list-wrapper">
      <el-row type="flex" :gutter="20" style="margin-top: 5px">
        <el-col
          :xs="24"
          :sm="12"
          :md="12"
          :lg="12"
          :xl="12"
          style="margin-bottom: 20px"
        >
          <el-row
            class="svg-sum-wrapper"
            :gutter="20"
            style="margin-left: -8px; margin-top: 0"
          >
            <el-col :span="12">
              <el-card>
                <div class="count">
                  <div>
                    <p style="color: var(--primary-green-color)">
                      {{ deptTotalCount }}
                    </p>
                    <p>单位总数</p>
                  </div>
                  <div>
                    <SvgIcon icon-class="home" />
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="12" style="margin-bottom: 20px">
              <el-card>
                <div class="count">
                  <div>
                    <p style="color: var(--primary-red-color-soft)">
                      {{ riskDeptCount }}
                    </p>
                    <p>风险单位</p>
                  </div>
                  <div class="">
                    <SvgIcon icon-class="alert-triangle" />
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card>
                <div class="count">
                  <div>
                    <p style="color: var(--primary-color)">
                      {{ assetBusinessCount }}
                    </p>
                    <p>业务系统</p>
                  </div>
                  <div>
                    <SvgIcon icon-class="settings" />
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card>
                <div class="count">
                  <div>
                    <p style="color: var(--primary-color)">
                      {{ assetResourceCount }}
                    </p>
                    <p>计算设备</p>
                  </div>
                  <div>
                    <SvgIcon icon-class="monitor" />
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-col>
        <el-col
          :xs="24"
          :sm="12"
          :md="12"
          :lg="12"
          :xl="12"
          style="margin-bottom: 20px; padding-right: 14px"
        >
          <el-card style="padding-left: 30px">
            <EchartPie
              :options="optionsBar"
              style="height: 213px; width: 100%"
            ></EchartPie>
          </el-card>
        </el-col>
      </el-row>
      <SelSearch
        ref="searchLabel"
        v-model="searchData"
        :menu-data="menuData"
        :items="items"
        :show-search-unit-tree="existDeptSearch && !hideSearchUnitTree"
        @search="() => selectOperatingDeptPage(true)"
      >
      </SelSearch>
      <el-tabs v-model="activeName" v-loading="loading">
        <el-tab-pane label="卡片视图" name="card">
          <DataWrapper :data="listData">
            <Card :list-data="listData"></Card>
          </DataWrapper>
        </el-tab-pane>
        <el-tab-pane label="列表视图2" name="list">
          <List :list-data="listData"></List>
        </el-tab-pane>
      </el-tabs>
      <SelPagination
        ref="paginationRef"
        style="margin-top: 20px"
        class="mt0-new"
        :total="total"
        :page-size="pageSize"
        @change="changePagination"
      ></SelPagination>
    </section>
    <!-- 内容 -->
  </ResizeWrapper>
</template>
<script setup lang="ts" name="Customer/list">
import useGetDeptId from "@/views/deptPerson/ts/getId";
import { FormItem, IStringObject, MenuData } from "@/config/types";
import List from "./components/indexList.vue";
import Card from "./components/indexCard.vue";
import {
  getOperatingDeptCount,
  getIndustryTypeChartForDept,
  getOperatingDeptPage,
  getOperatingDeptBusinessCount,
} from "@/api/customer/index";
import { getGetListTag } from "@/api/deptPerson/tags";
import { getIndustryType } from "@/api/deptPerson/common";
import useDeptTreeStore from "@/store/modules/deptTree";
import useTags from "@/views/customer/vulnManagePage/business/composition/tags";
import "@/assetEcharts";
import useUser from "@/store/modules/user";
import { getSelectAnalyst } from "@/api/testManage/taskPlan";
import { customerTypeList } from "@/config/constant";

const leftTreeType = computed(() => {
  return useUser().leftTreeType;
});

const route = useRoute();
const treeCollectionRef = ref<any>(null);
const resizeRef = ref<any>(null);
const paginationRef = ref<any>(null); // 分页组件引用
const hideSearchUnitTree = computed(() => {
  return resizeRef.value && resizeRef.value.hideSearchUnitTree;
});

const existDeptSearch = computed(() => {
  return useDeptTreeStore().existDeptSearch;
});
const { getId, areaDeptParams } = useGetDeptId();

// 单位数据统计
const searchLabel = ref<any>(null);
const deptTotalCount = ref(0);
const riskDeptCount = ref(0);
const assetBusinessCount = ref(0);
const assetResourceCount = ref(0);
const optionsBar = ref<IStringObject>({
  tooltip: {
    trigger: "item",
  },
  title: {
    text: "单位行业类型分布",
    show: true,
    textStyle: {
      fontSize: "14px",
      paddingLeft: "30px",
    },
    top: 0,
    left: 0,
  },
  legend: {
    left: "60%",
    bottom: "center",
    orient: "vertical",
    height: "100%",
    type: "scroll",
    itemHeight: 10,
    formatter: function (name: string) {
      const data = optionsBar.value.series[0].data;
      let total = 0;
      let tarValue;
      for (let i = 0; i < data.length; i++) {
        total += Number(data[i].value);
        if (data[i].name == name) {
          tarValue = data[i].value;
        }
      }
      const v = tarValue;
      const p = Math.round((v / total) * 100);

      return `{a|${name}    ${p}%}`;
    },
    textStyle: {
      rich: {
        a: {
          width: window.innerWidth < 1400 ? 75 : 100,
        },
      },
    },
  },
  xAxis: {
    show: false,
  },
  yAxis: {
    show: false,
  },
  series: [
    {
      name: "单位行业类型",
      type: "pie",
      radius: ["55%", "75%"],
      center: ["22%", "60%"],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: "center",
      },
      emphasis: {
        label: {
          show: true,
          fontSize: window.innerWidth < 1530 ? 16 : 20,
          fontWeight: "bold",
        },
      },
      labelLine: {
        show: false,
      },
      data: [],
    },
  ],
});
// 搜索内容
const searchData = ref<IStringObject>({
  type: "2",
  isClassifiedProtection: "",
  deptType: "",
  industryType: "",
  deptName: "",
  address: "",
  deptTreeId: "",
  customerType: "",
  deliveryManager: "",
});
const searchData2 = ref<IStringObject>({});

const menuData = ref<MenuData[]>([
  {
    prop: "isClassifiedProtection",
    label: "等保备案单位",
    optionList: [
      { label: "是", value: "1" },
      { label: "否", value: "0" },
    ],
    span: 12,
  },
  {
    prop: "deptType",
    label: "单位类型",
    dictName: "dept_type",
    span: 12,
  },
]);
const items = ref<FormItem[]>([
  {
    is: "el-input",
    prop: "deptName",
    label: "单位名称",
    span: 8,
  },
  {
    is: "el-input",
    prop: "address",
    label: "单位地址",
    span: 8,
  },
  {
    prop: "industryType",
    label: "行业类型",
    is: "type-tree",
    span: 8,
    option: {
      treeType: 1,
    },
  },
  {
    prop: "customerType",
    label: "客户类型",
    is: "el-select",
    span: 8,
    option: {
      optionList: customerTypeList,
    },
  },
  {
    prop: "deliveryManager",
    label: "交付经理",
    is: "el-select",
    span: 8,
    option: {
      optionList: [],
    },
  },
  {
    prop: "tagId",
    label: "单位标签",
    is: "el-select",
    span: 8,

    option: {
      getList: {
        api: getGetListTag,
        resKey: "rows",
        valueKey: "id",
        labelKey: "tagName",
      },
    },
  },
]);
function getIndustry() {
  getIndustryType().then((res: any) => {
    items.value[2].option.data = res.tree;
  });
}
getIndustry();
// const formItems = ref<FormItem[]>([
//   {
//     is: "el-input",
//     prop: "deptName",
//     label: "单位名称",
//     span: 12,
//   },
//   {
//     is: "el-input",
//     prop: "address",
//     label: "单位地址",
//     span: 12,
//   },
// ]);
// 获取行业类型数据

const activeName = ref("card");
// 获取运营单位统计
function selectOperatingDeptCount() {
  getOperatingDeptCount({ type: "2", ...areaDeptParams.value }).then(
    (res: any) => {
      deptTotalCount.value = res.deptTotalCount;
      riskDeptCount.value = res.riskDeptCount;
      assetBusinessCount.value = res.assetBusinessCount;
      assetResourceCount.value = res.assetResourceCount;
    }
  );
}
selectOperatingDeptCount();
// 单位行业类型分布
function selectIndustryTypeChartForDept() {
  getIndustryTypeChartForDept({ ...areaDeptParams.value }).then((res: any) => {
    const arr = [];
    for (const i in res) {
      arr.push({
        value: res[i],
        name: i,
      });
    }
    optionsBar.value.series[0].data = arr;
  });
}
selectIndustryTypeChartForDept();
// 获取单位列表
const total = ref(0);
const pageSize = ref(10);
const pageNum = ref(1);
const listData = ref<IStringObject[]>([]);
function changePagination(obj: IStringObject) {
  pageSize.value = obj.pageSize;
  pageNum.value = obj.pageNum;
  selectOperatingDeptPage();
}

const loading = ref(false);
let loadingTimer: ReturnType<typeof setTimeout> | null = null;

// 查询
// 创建一个请求控制器管理器
const abortController = ref<AbortController | null>(null);

// 获取业务数量的函数
async function fetchBusinessCounts(rows: any[]) {
  // 如果存在之前的请求，取消它
  if (abortController.value) {
    abortController.value.abort();
  }

  // 创建新的 AbortController
  abortController.value = new AbortController();
  const signal = abortController.value.signal;

  try {
    for (let i = 0; i < rows.length; i++) {
      // 检查是否已被取消,监测到被取消后，后面的请求不再执行
      if (signal.aborted) {
        console.log("请求被取消，停止后续请求");
        break;
      }

      const item = rows[i];
      try {
        const count = await getOperatingDeptBusinessCount(
          {
            deptId: item.deptId,
          },
          // 传入 signal 到请求中
          { signal }
        );

        // 再次检查是否已被取消，避免更新已过期的数据
        if (!signal.aborted) {
          listData.value[i] = {
            ...listData.value[i],
            businessCount: count,
          };
        }
      } catch (error) {
        // 如果是取消导致的错误，直接退出循环
        if (
          (error instanceof DOMException && error.code === 20) || // 标准 AbortError
          (error instanceof Error && error.toString().includes("abort")) || // 一般的取消错误
          signal.aborted // 直接检查信号状态
        ) {
          console.log("请求被取消，停止后续请求");
          break;
        }
        // 其他错误继续处理下一条数据
        console.error(`获取 ${item.deptId} 的业务数量失败:`, error);
      }
    }
  } catch (error) {
    console.error("获取业务数量失败:", error);
  }
}
function selectOperatingDeptPage(resetPage = false) {
  // 如果需要重置分页，将页码重置为第1页
  if (resetPage && paginationRef.value) {
    paginationRef.value.resetPageNum();
    pageNum.value = 1;
  }

  if (
    !hideSearchUnitTree.value ||
    (resizeRef.value && !searchData.value.deptTreeId)
  ) {
    useDeptTreeStore().updateDeptId(searchData.value.deptTreeId, route.name);

    if (useDeptTreeStore().existDeptSearch) {
      const deptTreeType =
        useDeptTreeStore().deptSelectData[route.name].deptTreeType;
      areaDeptParams.value.params = {
        deptTreeId: searchData.value.deptTreeId,
        deptTreeType: deptTreeType,
      };
    }

    selectOperatingDeptCount();
    selectIndustryTypeChartForDept();
  }

  loading.value = true;
  getOperatingDeptPage({
    ...searchData.value,
    ...searchData2.value,
    ...areaDeptParams.value,
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    _abort: 1,
  })
    .then((res: any) => {
      loading.value = false;
      total.value = res.total;
      // 先展示基础数据
      listData.value = res.rows;
      // 使用外部定义的 fetchBusinessCounts 函数
      fetchBusinessCounts(res.rows);
    })
    .catch(() => {
      loadingTimer && clearTimeout(loadingTimer);
      loadingTimer = setTimeout(() => {
        loading.value = false;
      }, 200);
    });
}

selectOperatingDeptPage();

watch(areaDeptParams, () => {
  searchData.value.deptTreeId = areaDeptParams.value.params.deptTreeId;
  // 左侧树切换时重置分页到第1页
  if (paginationRef.value) {
    paginationRef.value.resetPageNum();
    pageNum.value = 1;
  }
  selectOperatingDeptCount();
  selectIndustryTypeChartForDept();
  selectOperatingDeptPage();
});
// watch(
//   () => searchData.value,
//   () => {
//     selectOperatingDeptPage();
//   },
//   {
//     deep: true,
//   }
// );

//标签
useTags(items.value, "dept");

// Define a local version of getDeliveryManagerOptions function
function getLocalDeliveryManagerOptions() {
  getSelectAnalyst({
    perms: "permission:deliveryManager",
  }).then((res: any) => {
    const deliveryManagerItem = items.value.find(
      (item) => item.prop === "deliveryManager"
    );
    if (!deliveryManagerItem) return;

    deliveryManagerItem.option.optionList = res.analystList?.map(
      ({ userId, nickName }: any) => ({
        value: userId,
        label: nickName,
      })
    );
  });
}
getLocalDeliveryManagerOptions();
</script>
<style scoped lang="scss">
:deep(.el-card) {
  background: #ffffff;
  box-shadow: 0px 2px 5px 2px rgba(0, 0, 0, 0.07);
  border-radius: 8px;
  border: none;
}
:deep(.card:first-child) {
  margin-top: 10px;
}
.count {
  display: flex;
  justify-content: space-between;
  padding: 5px;
  > div {
    &:nth-child(1) {
      > p {
        &:nth-child(1) {
          font-size: 28px;
          padding-bottom: 8px;
          font-weight: 600;
        }
        &:nth-child(2) {
          margin-top: 10px;
          font-size: 12px;
          font-weight: 500;
        }
      }
    }
    &:nth-child(2) {
      margin-top: 10px;
      width: 56px;
      height: 56px;
      background: url("@/assets/images/customerCountBg.png");
      background-size: 100% 100%;
      position: relative;
      > .svg-icon {
        width: 16px;
        height: 16px;
        color: var(--primary-color-sub-3);
        position: absolute;
        top: 20px;
        left: 20px;
      }
    }
  }
}
:deep .btns-box {
  & > section {
    width: calc(100% - 110px);
  }
}
.searchList {
  width: 100%;
  :deep .el-form {
    width: calc(100% - 150px);
    float: left;
  }
  > .pull-right {
    padding-top: 25px;
  }
}
</style>
