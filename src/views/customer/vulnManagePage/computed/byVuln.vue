<!--按漏洞显示-->

<template>
  <!-- 批量操作按钮 -->
  <BatchAction
    :type="2"
    :selection="tableSelection"
    @openDialog="(type) => batchAction('', type)"
    @update="updateCurrentPage"
  ></BatchAction>
  <SelTable
    ref="tableRef"
    row-key="id"
    :checkbox="true"
    :columns="columns"
    :load-data="getSelectBasicResourceVulnListPage"
    :default-params="{ ...deptParams, ...searchData, _abort: 1 }"
    @tableLoaded="changeActionFlag"
  >
    <template #title="{ row }">
      <div class="sel-clickable" @click="toDetail(row)">
        {{ row.title }}
      </div>

      <SelTagList
        :ref="(el: any) => setRef(el, row.id)"
        :del-tag="getDeleteVulnTag"
        :check-params="{ id: row.id }"
        del-key="vulnTagRelId"
        del-key-name="vulnTagRelId"
        permi="vulnManage:vulnTag:action"
        :list="row.tagNameList"
        :get-data="getSelectVulnTagById"
        view-permi="vulnManage:vulnTag:list"
      ></SelTagList>
    </template>
    <template #level="{ row }">
      <el-tag :type="LEVEL_TYPE[row.level]">{{
        getDictLabel(vuln_level, row.level)
      }}</el-tag>
    </template>
    <!-- 操作按钮 -->
    <template #actions="scope">
      <ActionColumn
        :type="2"
        :scope="scope"
        :action-flag="actionFlag"
        @openDialog="(type, id) => singleAction('', type, id)"
        @update="updateCurrentPage"
        @changeActionWidth="changeActionWidth"
        @openSelTag="openSelTag"
      ></ActionColumn>
    </template>
  </SelTable>
  <!-- 操作弹框 -->
  <ActionDialog
    ref="actionDialogRef"
    :action-type="actionType"
    :ids="isBatch ? tableSelection : selectionIds"
    :type="2"
    :is-batch="isBatch"
    :parent-id="parentId"
    @update="updateCurrentPage"
  ></ActionDialog>
</template>

<script setup lang="ts">
import { getSelectBasicResourceVulnListPage } from "@/api/customer/vuln/computed";
import { LEVEL_TYPE } from "@/config/constant";
import { useDict, getDictLabel } from "@/utils/dict";
import {
  getDeleteVulnTag, //删除标签
  getSelectVulnTagById, //通过漏洞id查询标签
} from "@/api/vuln/tags";
import ActionColumn from "../business/actionColumn.vue";
import BatchAction from "../business/batchAction.vue";
import ActionDialog from "../business/actionDialog.vue";

import useAction from "../business/composition/action";
import useColumns from "./composition/columns";
import useSetActionsWidth from "../business/composition/setActionsWidth";

import { useGetAreaDeptParams } from "@/utils/areaDeptParams";

interface Props {
  areaDeptParams?: { [index: string]: any };
  searchData?: { [index: string]: any };
}

const props = withDefaults(defineProps<Props>(), {
  areaDeptParams() {
    return {};
  },
  searchData() {
    return {};
  },
});

const emit = defineEmits(["update", "openSelTag"]);

const { deptParams } = useGetAreaDeptParams(props);

const { vuln_level } = useDict("vuln_level");
const { childColumns: columns, toDetail } = useColumns();

const { actionFlag, changeActionFlag, changeActionWidth } =
  useSetActionsWidth(columns);

const {
  tableRef,
  tableSelection,
  actionType,
  actionDialogRef,

  isBatch,
  parentId,
  singleAction,
  batchAction,
  selectionIds,
  updateTable,
} = useAction();

columns.value.splice(2, 0, {
  prop: "assetsName",
  label: "影响资产名称",
  className: "",
  width: 150,
  clickDept: "assetsComputedName",
});

function updateCurrentPage() {
  emit("update");
}

const vulnId = ref("");
function openSelTag(param: any) {
  console.log(param);
  vulnId.value = param.vulnId;
  emit("openSelTag", param);
}
const tagRefs = ref<any>({});
// 选择后更新
function updateTag() {
  console.log(tagRefs.value);

  tagRefs.value[vulnId.value].update();
}
function resetVulnId() {
  vulnId.value = "";
}
function setRef(el: any, id: string) {
  if (el) {
    tagRefs.value[id] = el;
  }
}
defineExpose({
  update: updateTable,
  resetTagDialog: resetVulnId,
  searchTagByVulnId: updateTag,
});
</script>

<style scoped lang="scss"></style>
