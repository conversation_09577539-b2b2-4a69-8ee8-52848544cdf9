<template>
  <el-card class="group-template">
    <template #header>通知渠道模板配置</template>
    <SelTable
      ref="tableRef"
      :columns="tableColumns"
      :load-data="getGetTemplateList"
    >
      <template #content="{ row }">
        <el-input
          v-model="row.content"
          style="width: calc(100% - 50px)"
          class="pull-left"
        />
        <SvgIcon
          icon-class="save"
          class="pull-left"
          style="
            color: #ffd439;
            margin-left: 10px;
            width: 16px;
            height: 16px;
            margin-top: 8px;
            cursor: pointer;
          "
          @click="saveContent(row)"
        />
      </template>
    </SelTable>
    <SelDialog v-model="uploadDialog" title="文件上传" @submit="saveFile">
      <SelForm
        ref="uploadFormRef"
        v-model="uploadFormData"
        :items="uploadFormItems"
      ></SelForm>
    </SelDialog>
  </el-card>
</template>
<script setup lang="ts">
import {
  getGetTemplateList,
  postAddOrUpdateTemplate,
} from "@/api/operationManage/globalNoticeChannel/groupTemplate";
import { ColumnItem, FormItem, IStringObject } from "@/config/types";
const uploadDialog = ref(false);
const uploadFormData = ref<IStringObject>({
  id: "",
  templateType: "",
  templateFile: "",
  content: "",
});
const uploadFormRef = ref<any>(null);
const uploadFormItems = ref<FormItem[]>([
  {
    is: "upload",
    label: "文件上传",
    prop: "templateFile",
    required: true,
    option: {
      accept: ".html",
      fileType: ["html"],
      data: {
        deptId: "1",
      },
      uploadUrl: "/file/uploadFileForGroup",
    },
  },
]);
const tableRef = ref<any>(null);
const tableColumns = ref<ColumnItem[]>([
  { label: "模板类型", prop: "templateTypeName" },
  { label: "模板文件名称", prop: "templateFileName" },
  { label: "发送文本", slotName: "content" },
  {
    label: "上传",
    slotName: "action",
    width: "60px",
    btnList: [
      {
        icon: "upload",
        title: "上传",
        onClick({ row }) {
          uploadFormData.value = {
            id: row.id,
            templateType: row.templateType,
            templateFile: "",
            content: row.content,
          };
          uploadDialog.value = true;
        },
      },
    ],
  },
]);
function saveFile() {
  uploadFormRef.value.validate().then(() => {
    addOrUpdateTemplate(uploadFormData.value);
  });
}
// 保存发送内容
function saveContent(row: any) {
  const data = {
    id: row.id,
    templateType: row.templateType,
    templateFile: row.templateFile,
    content: row.content,
  };
  addOrUpdateTemplate(data);
}
function addOrUpdateTemplate(data: any) {
  data._msg = 1;
  postAddOrUpdateTemplate(data).then(() => {
    tableRef.value.reload();
    uploadDialog.value = false;
  });
}
</script>
<style lang="scss" scoped>
.group-template {
  :deep(.action-svg) {
    height: 36px;
    line-height: 36px;
  }
}
</style>
