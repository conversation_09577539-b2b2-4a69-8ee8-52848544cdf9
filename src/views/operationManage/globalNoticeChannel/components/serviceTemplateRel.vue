<template>
  <FooterPage
    :show-cancel="false"
    submit-text="保存"
    @submit="saveServiceTemplateInfo"
  >
    <div style="width: 40%; margin-left: 30px">
      <SelForm
        v-model="templateData"
        :items="tempalteFormItems"
        label-position="left"
        label-width="100px"
      ></SelForm>
    </div>
  </FooterPage>
</template>
<script setup lang="ts">
import { IStringObject, FormItem } from "@/config/types";
import { getEmailList } from "@/api/operationManage/globalNoticeChannel/messageAndEmailConfig";
import {
  getServiceTemplateRelInfo,
  postServiceTemplateRelSave,
} from "@/api/operationManage/globalNoticeChannel/messageAndEmailTemplateConfig";
const templateData = ref<IStringObject>({
  emailTemplateId: "",
  emailAddrId: "",
  smsSignatureId: "",
  taskRemindState: null,
});
// 获取服务模板详情内容
function getServiceTemplateInfo() {
  getServiceTemplateRelInfo().then((res: any) => {
    templateData.value = {
      emailTemplateId: res.emailTemplateId,
      emailAddrId: res.emailAddrId,
      smsSignatureId: res.smsSignatureId,
      taskRemindState: res.taskRemindState ? res.taskRemindState : null,
    };
  });
}
getServiceTemplateInfo();
const tempalteFormItems = ref<FormItem[]>([
  {
    is: "el-select",
    label: "选择邮箱",
    prop: "emailAddrId",
    option: {
      getList: {
        api: getEmailList,
        resKey: "rows",
        valueKey: "id",
        labelKey: "emailAddr",
        params: {
          openState: 1,
        },
      },
    },
    span: 24,
  },
  {
    is: "el-select",
    prop: "emailTemplateId",
    label: "邮箱模板类型",
    option: {
      dictName: "email_template_id",
    },
    span: 24,
  },
  {
    is: "el-select",
    prop: "smsSignatureId",
    label: "短信签名",
    option: {
      dictName: "sms_signature_id",
    },
    span: 24,
  },
  {
    is: "el-switch",
    prop: "taskRemindState",
    label: "任务提醒",
    option: {
      activeValue: 1,
      inactiveValue: 0,
      // activeText: "任务提醒",
    },
  },
]);
function saveServiceTemplateInfo() {
  if (!templateData.value.emailAddrId) {
    ElMessage.warning("请选择邮箱！");
    return false;
  }
  if (!templateData.value.emailTemplateId) {
    ElMessage.warning("请选择邮箱模板类型！");
    return false;
  }
  if (!templateData.value.smsSignatureId) {
    ElMessage.warning("请选择短信签名！");
    return false;
  }
  postServiceTemplateRelSave({ ...templateData.value, _msg: 1 }).then(() => {
    getServiceTemplateInfo();
  });
}
</script>
