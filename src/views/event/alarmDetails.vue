<template>
  <el-card>
    <div class="title-bottom-line">
      <p class="pull-left">告警任务信息</p>
      <el-button
        v-hasPermi="'search:query:view'"
        class="pull-right"
        @click="traceDetail"
      >
        <el-icon><Position /></el-icon>
        追溯
      </el-button>
      <div class="clearfix"></div>
    </div>
    <el-form
      ref="form"
      label-width="140px"
      label-position="left"
      class="base-info-form"
    >
      <el-row :gutter="70">
        <el-col :span="24">
          <el-form-item label="告警任务编号：">{{
            details.alertNo
          }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="告警任务名称：">{{
            details.title
          }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <!-- <el-form-item label="告警级别：" v-if="details.priority == 5">紧急</el-form-item>
          <el-form-item label="告警级别：" v-if="details.priority == 4">高危</el-form-item>
          <el-form-item label="告警级别：" v-if="details.priority == 3">中危</el-form-item>
          <el-form-item label="告警级别：" v-if="details.priority == 2">低危</el-form-item>
          <el-form-item label="告警级别：" v-if="details.priority == 1">信息</el-form-item> -->
          <!-- <el-form-item label="告警级别：">{{ details.priorityStr }}</el-form-item> -->
          <el-form-item label="告警级别：">
            <el-tag :type="levelData[details.priority]">{{
              details.priorityStr
            }}</el-tag>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="告警发生设备：">{{
            details.devName
          }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="设备地址：">{{ details.devIp }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="设备类型：">{{ details.devType }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="告警内容描述：">{{
            details.description
          }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <LastStageTime :data="details" />
          <!--  <el-form-item
            v-if="details.stage == '1'"
            label="首轮告警发生时间："
            >{{ details.createTime }}</el-form-item
          >
          <el-form-item
            v-if="details.stage == '2'"
            label="二轮告警发生时间："
            >{{ details.createTime2 }}</el-form-item
          >
          <el-form-item
            v-if="details.stage == '3'"
            label="三轮告警发生时间："
            >{{ details.createTime3 }}</el-form-item
          >
          <el-form-item
            v-if="details.stage == '4'"
            label="四轮告警发生时间："
            >{{ details.createTime4 }}</el-form-item
          > -->
        </el-col>
        <el-col :span="24">
          <el-form-item label="首条日志源地址">{{
            details.csrcip
          }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="首条日志目的地址">{{
            details.cdstip
          }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="采集器地址：">{{ details.collIp }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="客户标识：">{{
            details.customerSign
          }}</el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
  <el-card>
    <div class="title-bottom-line">
      <p>报送状态</p>
    </div>
    <p v-if="details.lunci !== null && details.lunci !== ''">
      该告警为第{{ details.lunci }}轮告警，当前共计{{ details.num }}次告警
    </p>
    <p v-if="details.lunci === null || details.lunci === ''">
      该告警还未分配，当前共计{{ details.num }}次告警
    </p>
  </el-card>
  <!-- <el-card>
    <div class="title-bottom-line">
      <p>辅助分析</p>
    </div>
    <el-form
      ref="form"
      label-width="140px"
      label-position="left"
      class="base-info-form"
    >
      <el-row :gutter="70">
        <el-col :span="24">
          <el-form-item label="绑定域名：">{{
            details.reverseDomains
          }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="情报类型：">{{
            details.categories
          }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="情报可行度：">{{
            details.threatScore
          }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="位置：">{{ details.location }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="可疑度：">{{ details.score }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="推断原因：">{{ details.reason }}</el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </el-card> -->
  <el-card>
    <div class="title-bottom-line">
      <p>关联资产</p>
    </div>
    <ul
      v-if="details.assets !== undefined && details.assets.length > 0"
      class="task-list"
    >
      <li v-for="item in details.assets" :key="item.id" class="list-primary">
        {{ item.name }}
        <span class="pull-right">{{ item.ips }}</span>
      </li>
    </ul>
    <p v-else>无相关资产</p>
  </el-card>
  <el-card>
    <div class="title-bottom-line">
      <p>疑似关联事件2</p>
    </div>
    <ul
      v-if="
        details.eventAffectedAssetsMiddles !== undefined &&
        details.eventAffectedAssetsMiddles.length > 0
      "
      class="task-list"
    >
      <li
        v-for="item in details.eventAffectedAssetsMiddles"
        :key="item.id"
        class="list-primary"
      >
        {{ item.eventTitle }}
      </li>
    </ul>
    <p v-else>无疑似关联事件</p>
  </el-card>
  <el-card>
    <div class="title-bottom-line">
      <p>误报原因</p>
    </div>
    <XelTable
      v-if="getworkbenchList.length"
      ref="tableRef"
      :columns="columns"
      :data="getworkbenchList"
      :pagination="false"
      @selection-change="handleSelectionChange"
    >
    </XelTable>
  </el-card>

  <el-drawer v-model="traceDrawer" :before-close="handleCloseDetail">
    <AlarmTraceDetail
      v-if="traceDrawer"
      ref="log_detail"
      :trace-data="traceData.data"
    ></AlarmTraceDetail>
  </el-drawer>
</template>
<script>
export default {
  name: "AlarmDetails",
  components: { alarmTraceDetail },
};
</script>

<script setup>
import { ref, reactive, toRefs, onMounted } from "vue";
import { alertDetail, goMisreportAlertDetail } from "@/api/workSpace/home.js";
import { useRouter, useRoute } from "vue-router";
import { Level_priority } from "@/config/constant";
import AlarmTraceDetail from "./alarmTraceDetail.vue";
import alarmTraceDetail from "./alarmTraceDetail.vue";
import LastStageTime from "./threatEvent/components/lastStageTime.vue";

const state = reactive({
  levelData: Level_priority,
});
const { levelData } = toRefs(state);
const router = useRouter();
const route = useRoute();
const details = ref({});
const tableRef = ref();
const getworkbenchList = ref([]);
const columns = [
  {
    prop: "userName",
    label: "操作人",
  },
  {
    prop: "endTimeStr",
    label: "操作时间",
  },
  {
    prop: "misreportReason",
    label: "误报原因",
  },
  {
    prop: "stage",
    label: "轮次",
  },
  {
    prop: "createTime",
    label: "发生时间",
  },
  {
    prop: "num",
    label: "告警次数",
  },
];
function getalertDetail() {
  alertDetail({ id: route.params.id }).then((res) => {
    details.value = res.data;
  });
  goMisreportAlertDetail({ id: route.params.id }).then((res) => {
    getworkbenchList.value = res.alertList;
  });
}
getalertDetail();
const traceDrawer = ref(false);
const traceData = reactive({
  data: {
    cruleid: "",
    alertId: "",
    alertTime: "",
  },
});

function traceDetail() {
  traceDrawer.value = true;
  traceData.data.cruleid = details.value.cruleid.split("_")[1];
  traceData.data.alertId = details.value.alertNo;
  traceData.data.alertTime = details.value.createTime;
}
function handleCloseDetail() {
  traceDrawer.value = false;
}
</script>
<style lang="scss" scoped>
.base-info-form {
  :deep(.el-form-item) {
    border-bottom: 1px solid #ebedf1;
    // margin-top: 10px;
    margin-bottom: 10px;
    padding-bottom: 10px;
    // margin-left: 20px;
  }
}
.el-card {
  margin-bottom: 20px;
}
</style>
