<template>
  <!-- 操作按钮 -->
  <div class="btns-box text-right">
    <el-button size="mini" @click="toLogList">
      <el-icon class="mr4"> <Tickets /></el-icon>
      任务日志汇总</el-button
    >
    <el-button
      v-if="isManageTask == 0 && eventTaskFlag1"
      size="mini"
      @click="openadd"
    >
      <el-icon class="mr4"><Plus /></el-icon>
      添加任务</el-button
    >
    <el-button
      v-if="isManageTask == 1 && eventTaskFlag2"
      size="mini"
      @click="openadd"
    >
      <el-icon class="mr4"><Plus /></el-icon>
      添加任务</el-button
    >
    <el-button v-if="bachAuditFlag" size="mini" @click="batchCheck">
      <el-icon class="mr4"> <Checked /></el-icon>
      批量审核</el-button
    >
  </div>

  <section v-for="(item, index) in taskGroupList" :key="index">
    <p class="title">{{ item.group }}({{ item.total }}项任务)</p>
    <XelTable
      class="task-list-table"
      :pagination="false"
      :data="item.taskList || []"
      :columns="columns"
      :row-key="getParentRowKeys"
      @expand-change="parentExpand"
    >
      <template #expand="{ row }">
        <div class="info-box">
          <p>
            <span class="label">任务要求：</span>
            <span
              class="span-html"
              v-html="$replaceToken(row.description)"
            ></span>
          </p>
          <p>
            <span class="label">审核状态：</span>
            <span class="margin-left5" v-html="getAuditComment(row)"></span>
          </p>
          <p>
            <span class="label">执行日志：</span>
            <LogGroup
              style="width: 100%"
              :list="row.logList"
              :show-edit="isEventTab"
              :can-update-log="isEventTab && taskDetail.canUpdateLog == 'Y'"
              @delFile="delFile"
              @openEditLog="openEditLog"
              @delLog="delLog"
            ></LogGroup>
          </p>
          <SiemLogGroup :eventTaskId="row.id"></SiemLogGroup>
        </div>
      </template>
      <template #auditStatus="{ row }">
        <AuditStatus :status="row.auditStatus"></AuditStatus>
      </template>
      <template #taskName="{ row }">
        <div
          :class="{
            'xel-clickable': eventStore.eventInfo.status != 0,
          }"
          class="task-name"
          @click="toDetail(row)"
        >
          {{ row.title }}
        </div>
      </template>
      <template #btns="scope">
        <XelHandleBtns
          :btn-list="getBtnList(scope.row)"
          :scope="scope"
        ></XelHandleBtns>
      </template>
    </XelTable>
  </section>

  <!-- 任务审核弹框 -->
  <XelDialog ref="auditDialogRef" title="批量审核" @submit="batchCheckTask">
    <el-form ref="checkFormRef" style="padding: 0 10px" :model="checkForm">
      <el-row
        v-for="(item, index) in checkList"
        :key="index"
        class="margin-bottom10"
        :gutter="20"
      >
        <el-col :span="12">
          <el-form-item label="任务标题："> {{ item.title }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <XelFormItem
            v-model="checkForm[item.id]"
            label="激励加分"
            :prop="item.id"
            form-type="number"
            @input="(val) => changeReward(val, item.id)"
          ></XelFormItem>
        </el-col>
      </el-row>
    </el-form>
  </XelDialog>

  <!-- 添加任务 -->
  <XelDialog ref="addDialogRef" title="添加任务" @submit="addTaskFn">
    <AddTask
      v-if="addFormStatus"
      ref="addTaskRef"
      :hide-btn="true"
      :add-item="saveEventTask"
      :other-params="{ eventId: $route.params.id, isManageTask: isManageTask }"
      @close="closeAdd"
    ></AddTask>
  </XelDialog>
</template>
<script setup>
import LogGroup from "./logGroup.vue";
import SiemLogGroup from "./siemLogGroup.vue";

import { ref, computed, watch, nextTick } from "vue";
import { ElMessage } from "element-plus";

import AddTask from "./addTask.vue";
import taskHandle from "../mixins/taskHandle";
import useEventStore from "@/store/modules/eventDetail";
import AuditStatus from "../../composition/auditStatus.vue";
import {
  getLogList,
  initTaskList,
  saveEventTaskAudit,
  saveEventTask,
  selectNotAuditedTask,
} from "@/api/event/task.js";
import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();
const eventStore = useEventStore();
//更新页面
const taskUpdate = computed(() => {
  return eventStore.taskUpdate;
});
watch(
  () => taskUpdate.value.status,
  () => {
    getList();
  }
);

const { handlerTask } = taskHandle();

const props = defineProps({
  isManageTask: {
    type: String,
    default: "",
  }, //0 分析 1处置
  tabName: {
    type: [String, Number],
    default: 0,
  },
  selfName: {
    type: String,
    default: "",
  },
});

watch(
  () => props.tabName,
  (val) => {
    if (val == props.selfName) {
      getList();
    }
  }
);
const bachAuditFlag = computed(() => {
  return eventStore.eventDetail.bachAuditFlag == "Y";
});
//事件分析页 添加任务权限
const eventTaskFlag1 = computed(() => {
  return eventStore.eventDetail.taskAdd == "Y";
});
const eventTaskFlag2 = computed(() => {
  return eventStore.eventDetail.manageAdd == "Y";
});

const taskGroupList = ref([]);
getList();
function getList() {
  initTaskList({
    eventId: route.params.id,
    isManageTask: props.isManageTask,
  }).then((data) => {
    console.log("data: ", data);
    eventStore.eventDeptList =
      data.taskGroupList.length > 0 &&
      data.taskGroupList[0].eventDeptList.map((item) => item.deptId);
    taskGroupList.value = data.taskGroupList.filter((item) => item.total);

    eventStore.changeTabNumsByName({
      name: props.isManageTask == 0 ? 1 : 2,
      num:
        (data.taskGroupList.length > 0 && data.taskGroupList[0].taskCount) || 0,
    });
    taskGroupList.value[0].taskList.map((tItem) => {
      return {
        ...tItem,
        logList: [],
      };
    });
  });
}

const columns = ref([
  {
    type: "expand",
    slotName: "expand",
  },
  { prop: "taskGroupText", label: "阶段", width: "100px" },
  {
    prop: "title",
    label: "任务",
    slotName: "taskName",
  },
  { prop: "createTime", label: "创建时间" },
  { prop: "assigneeName", label: "分析师" },
  { prop: "auditStatus", label: "审核", slotName: "auditStatus" },
  { prop: "auditName", label: "审核人" },
  {
    label: "操作",
    fixed: "right",
    slotName: "btns",
    width: "160px",
  },
]);

function getAuditComment(row) {
  let str = "";
  const auditStatus = Number(row.auditStatus);
  if ([0, 1, 2].includes(auditStatus)) {
    str = "未审核";
  } else {
    if (row.auditName) {
      console.log("row: ", row);
      if ([3, 4].includes(auditStatus)) {
        str = `<span class="margin-left10">审核人：${
          row.auditName
        }</span><span class="margin-left10">审核意见：${
          row.auditComment || ""
        }</span>`;
        if (auditStatus == 4) {
          str = `审核不通过 ` + str;
        } else if (auditStatus == 3) {
          str = `审核通过 ` + str;
        }
        // else if (auditStatus == 2) {
        //   str = `上次审核结果：<span class="margin-left10">审核通过</span>` + str;
        // }
      }
    } else {
      if (auditStatus == 3) {
        str = "审核通过";
      }
    }
  }
  return str;
}

function getBtnList(row) {
  return [
    {
      hide: row.startFlag != "Y",
      icon: "arrow-right",
      title: "开始",
      onClick(scope) {
        handlerTask("start", scope.row, props.isManageTask, true, eventStore);
      },
    },
    {
      hide: row.submitFlag != "Y",

      icon: "arrow-upward-outline",
      title: "提报",
      onClick(scope) {
        handlerTask("submit", scope.row, props.isManageTask, true, eventStore);
      },
    },
    {
      hide: row.deleteFalg != "Y",
      icon: "trash-2",
      title: "删除",
      onClick(scope) {
        handlerTask("del", scope.row, props.isManageTask, true, eventStore);
      },
    },
    {
      hide: row.reAuditFlag != "Y",
      icon: "flip-2-outline",
      title: "重审",
      onClick(scope) {
        toDetail(scope.row, { reAudit: true });
      },
    },
    {
      hide: row.closeFlag != "Y",
      icon: "close-circle-outline",
      title: "关闭",
      onClick(scope) {
        handlerTask("close", scope.row, props.isManageTask, true, eventStore);
      },
    },
    {
      hide: row.redoFlag != "Y",
      icon: "corner-up-left-outline",
      title: "重做",
      onClick(scope) {
        handlerTask("redo", scope.row, props.isManageTask, true, eventStore);
      },
    },
    {
      width: "300px",
      hide: row.auditFlag != "Y",
      icon: "checkmark-circle-2-outline",
      title: "审核",
      onClick(scope) {
        toDetail(scope.row, { audit: true });
      },
    },
  ];
}

//批量审核
const checkList = ref([]);
const auditDialogRef = ref();
const checkFormRef = ref();
const checkForm = ref({});
async function batchCheck() {
  const { taskList } = await selectNotAuditedTask({
    eventId: route.params.id,
    isManageTask: props.isManageTask,
  });
  checkList.value = taskList;
  if (checkList.value.length == 0) {
    ElMessage.warning("没有可审核的任务");
    return;
  }
  checkForm.value = {};
  for (const item of checkList.value) {
    checkForm.value[item.id] = 0;
  }
  auditDialogRef.value.open();
}

function batchCheckTask() {
  checkFormRef.value.validate((valid) => {
    if (valid) {
      const eventTaskList = [];

      for (const id in checkForm.value) {
        eventTaskList.push({
          id,
          reward: checkForm.value[id],
        });
      }

      for (const id in checkForm.value) {
        if (!checkForm.value[id] && checkForm.value[id] != 0) {
          ElMessage.warning("激励加分请输入-10000 到 10000之间的整数");
          return;
        }
      }

      saveEventTaskAudit({ eventTaskList, id: route.params.id }).then(() => {
        ElMessage.success("审核成功!");
        getList(); //刷新

        auditDialogRef.value.close();
      });
    }
  });
}

//任务日志汇总
function toLogList() {
  eventStore.pushEventTab({
    name: "logList" + props.isManageTask, //任务id或者其他唯一值
    label: "任务日志汇总",
    type: "logList", //确定组件
    isManageTask: props.isManageTask,
  });
}

//添加任务
const addDialogRef = ref();
const addFormStatus = ref(false);
function openadd() {
  // if (eventStore.eventInfo.isLock == 1) {
  //   ElMessage.warning("事件被抽检中，请抽检完毕后再新增或重做该事件的任务");
  //   return;
  // }
  addFormStatus.value = false;
  addDialogRef.value.open();
  nextTick(() => {
    addFormStatus.value = true;
  });
}
function closeAdd(a, update) {
  addDialogRef.value.close();
  if (update) {
    getList(); //刷新
  }
}
const addTaskRef = ref();
function addTaskFn() {
  addTaskRef.value.submitForm();
}

//打开详情tab
function toDetail(row, params = {}) {
  if (eventStore.eventInfo.status != 0) {
    eventStore.pushEventTab({
      name: row.id,
      label: row.title,
      type: "taskDetail",
      isManageTask: props.isManageTask,
      ...params,
    });
  }
}

function changeReward(value, id) {
  if (value > 10000 || value < -10000 || (!value && value != 0)) {
    checkForm.value[id] = null;
    ElMessage.warning("激励加分请输入-10000 到 10000之间的整数");
  }
  if (value) {
    nextTick(() => {
      checkForm.value[id] = parseInt(value);
    });
  }
}

const taskDetailState = reactive({
  logList: [],
});

// 任务展开
function getParentRowKeys(row) {
  return row.id;
}
function parentExpand(currentData) {
  taskGroupList.value.forEach((item) => {
    item.taskList.forEach(async (tItem) => {
      if (tItem.id == currentData.id) {
        tItem.logList = await getLogListFn(currentData.id);
      }
    });
  });
}
// 获取执行日志
async function getLogListFn(taskId) {
  const res = await getLogList({ eventTaskId: taskId });
  return res.rows;
}
</script>

<style lang="scss" scoped>
//威胁聚合---威胁事件---事件详情 --监测阶段
:deep(.el-table__expanded-cell) {
  top: 0px !important;
  border-bottom: 2px solid var(--primary-color) !important;
}
:deep(.el-table--small .el-table__cell) {
  padding: 0;
}
:deep(.el-table .cell) {
  padding: 8px 0;
}
.btns-box {
  border-bottom: 2px dashed #ededed;
  padding: 10px 0 20px;
}
.title {
  margin: 20px 0;
}
.info-box {
  background: #f9f9f9;

  p {
    padding: 10px 20px;
    line-height: 30px;
    display: flex;
    border-bottom: 1px solid #ebedf1;
    .label {
      // color:var( --text-color)Soft;
      width: 5em;
      flex-shrink: 0;
    }
  }
}
.task-name {
  padding-right: 5px;
}
::v-deep(.span-html) {
  p {
    margin-top: 6px !important ;
  }
}
.task-list-table {
  :deep(table) {
    padding-bottom: 1px;
  }

  :deep(.el-table__expanded-cell:not(.a)) {
    top: 0px;
  }
  :deep(tr:last-child:has(.el-table__expanded-cell)) {
    padding-bottom: 2px;
  }
}
</style>
