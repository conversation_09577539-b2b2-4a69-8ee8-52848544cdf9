<!--事件详情内的漏洞，关联弹框内调用自身组件-->

<template>
  <el-tabs v-model="activeName">
    <el-tab-pane label="业务系统漏洞" name="first">
      <SelTable
        row-key="id"
        :checkbox="true"
        ref="tableBusinessRef"
        :btn-list="btnListBusiness"
        :columns="columnsBusiness"
        :load-data="postSelectFlatVulnBusinessPage"
        :search-option="searchOptionBusiness"
        :default-params="tableDefaultParams"
      >
        <template #level="{ row }">
          <el-tag :type="LEVEL_TYPE[row.level]">{{
            getDictLabel(vuln_level, row.level)
          }}</el-tag>
        </template>
      </SelTable>
    </el-tab-pane>
    <el-tab-pane label="计算设备漏洞" name="second">
      <SelTable
        :fit="true"
        row-key="id"
        :checkbox="true"
        ref="tableComputedRef"
        :btn-list="btnListComputed"
        :columns="columnsComputed"
        :load-data="getSelectBasicResourceVulnListPage"
        :search-option="searchOptionComputed"
        :default-params="tableDefaultParams"
      >
        <template #level="{ row }">
          <el-tag :type="LEVEL_TYPE[row.level]">{{
            getDictLabel(vuln_level, row.level)
          }}</el-tag>
        </template>
      </SelTable>
    </el-tab-pane>
  </el-tabs>
</template>

<script setup lang="ts">
import updateByTabName from "../mixins/updateByTabName";
import { postSelectFlatVulnBusinessPage } from "@/api/customer/vuln/business";
import { getSelectBasicResourceVulnListPage } from "@/api/customer/vuln/computed";

import { postSaveCancelEventRelateVuln } from "@/api/event/vuln";

import { LEVEL_TYPE } from "@/config/constant";
import { useDict, getDictLabel } from "@/utils/dict";

import { Btn } from "@/config/types";

import { ElMessageBoxFun } from "@/utils/elMessageBox";

import useColumnsBusiness from "@/views/customer/vulnManagePage/business/composition/columns";
import useColumnsComputed from "@/views/customer/vulnManagePage/computed/composition/columns";

import useBusinessSearchOptions from "@/views/customer/vulnManagePage/business/composition/searchOption";
import useComputedSearchOptions from "@/views/customer/vulnManagePage/computed/composition/searchOption";
import useEventStore from "@/store/modules/eventDetail";

let eventStore = useEventStore();

interface Props {
  eventRelateVulnParam: "isRelated" | "isNotRelated"; //查询已关联事件漏洞传isRelated，未关联事件漏洞传isNotRelated
}
const props = withDefaults(defineProps<Props>(), {});

const emit = defineEmits<{
  (e: "openDialog"): void;
}>();

const { vuln_level } = useDict("vuln_level");

updateByTabName(4, () => {
  updateTable();
});

const route = useRoute();
const eventId = route.params.id;

const activeName = ref("first");

const tableBusinessRef = ref<any>(null);
const tableComputedRef = ref<any>(null);

function getRef() {
  return ref<any>(null);
}

const tableDefaultParams = {
  eventId,
  eventRelateVulnParam: props.eventRelateVulnParam,
};

const searchOptionBusiness = ref(getSearchOption(useBusinessSearchOptions));
const searchOptionComputed = ref(getSearchOption(useComputedSearchOptions));

function getSearchOption(useFn: any) {
  const { searchData: modelValue, searchItems, menuData } = useFn();
  const options = {
    modelValue: modelValue.value,
    searchItems: searchItems.value,
    menuData: menuData.value,
    showFilter: false,
  };

  return options;
}

const { childColumns: columnsBusiness } = useColumnsBusiness();
const { childColumns: columnsComputed } = useColumnsComputed();

[columnsBusiness, columnsComputed].forEach((item) => {
  item.value.pop();
  delete item.value[0].slotName;
  if (item.value.length > 9) {
    item.value.splice(2, 0, {
      prop: "assetName",
      label: "影响资产名称",
    });
  } else {
    item.value.splice(2, 0, {
      prop: "assetsName",
      label: "影响资产名称",
    });
  }
  if (props.eventRelateVulnParam == "isNotRelated") {
    delete item.value[0].onClick;
  }
});

//事件关联漏洞权限
let eventVulnAdd = computed(() => {
  return eventStore.eventDetail.eventVulnAdd == "Y";
});
let eventVulnDel = computed(() => {
  return eventStore.eventDetail.eventVulnDel == "Y";
});

function getBtnList(type: "business" | "computed") {
  if (props.eventRelateVulnParam == "isNotRelated") return [];
  const tableRef = type == "business" ? tableBusinessRef : tableComputedRef;
  const typeName = type == "business" ? "业务系统" : "计算设备";
  const btnList: Btn[] = [
    {
      icon: "add",
      title: "关联漏洞",
      onClick() {
        emit("openDialog");
      },
      hide: !eventVulnAdd.value,
    },
    {
      icon: "delete",
      title: "取消关联",
      hide: !eventVulnDel.value,
      onClick() {
        const checkList = tableRef.value.checkList;
        if (checkList.length == 0) {
          ElMessage.warning(`请先选择${typeName}漏洞`);
          return;
        }

        ElMessageBoxFun(`确定取消关联选中的${typeName}漏洞`).then(() => {
          const params = {
            id: eventId,
            ids: checkList.map((item) => item.id).join(),
            title: checkList.map((item) => item.title).join(),
            _msg: 1,
          };
          postSaveCancelEventRelateVuln(params).then(() => {
            updateTable();
          });
        });
      },
    },
  ];

  return btnList;
}

function updateTable() {
  tableBusinessRef.value?.search();
  tableComputedRef.value?.search();
  tableBusinessRef.value?.clearSelection();
  tableComputedRef.value?.clearSelection();
}

const btnListBusiness = getBtnList("business");
const btnListComputed = getBtnList("computed");

defineExpose({
  selectionBusiness: computed(() => {
    return tableBusinessRef.value?.checkList;
  }),
  selectionComputed: computed(() => {
    return tableComputedRef.value?.checkList;
  }),
  updateTable,
});
</script>

<style scoped lang="scss"></style>
