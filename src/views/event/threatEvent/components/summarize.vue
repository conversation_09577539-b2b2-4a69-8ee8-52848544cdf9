<template>
  <MouseDisplay
    :play-name="'摘要'"
    :show-submit="true"
    :disaboy="!eventBtns.eventUpdate"
    @submit="saveBaseInfo"
  >
    <div class="mt20">
      <EditComponent ref="baseInfoRef" :edit-info="event"></EditComponent>
    </div>
    <template #display>
      <el-form
        ref="form"
        label-width="120px"
        label-position="left"
        class="base-info-form base-info-box"
      >
        <el-row :gutter="70">
          <el-col :span="12">
            <el-form-item label="标题：">{{ event.title }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="事件级别 ：">
              <el-tag :type="LEVEL_DATA[event.levelId]">{{
                event.levelName
              }}</el-tag>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="创建人：">{{ event.createName }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="推断发生时间："
              >{{ event.beginTimeStr }} - {{ event.endTimeStr }}</el-form-item
            >
          </el-col>
          <el-col :span="12">
            <el-form-item label="标签："
              ><el-tag v-for="item in event.eventTagList" :key="item.tagId"
                ><span class="dobule">{{ item.tagName }}</span></el-tag
              ></el-form-item
            >
          </el-col>
          <el-col :span="12">
            <el-form-item label="责任单位1："
              ><span
                v-for="item in event.eventDeptList"
                :key="item.deptId"
                class="title-add"
                v-clickDept="['deptName', { deptId: item.deptId }]"
                >{{ item.deptName }}
              </span></el-form-item
            >
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="主管单位："
              ><span> {{ event.parentDeptName }}</span></el-form-item
            >
          </el-col> -->
        </el-row>
      </el-form>
    </template>
  </MouseDisplay>
  <MouseDisplay
    :play-name="'事件描述'"
    :show-submit="true"
    :disaboy="!eventBtns.eventDepiction"
    @submit="saveBaseInfo1"
  >
    <div class="mt20">
      <el-form
        ref="consciouRef"
        :model="consciouData"
        label-width="0"
        size="mini"
      >
        <XelFormItem
          v-for="(item, index) in consciousness"
          :key="index"
          v-model="consciouData[item.prop]"
          v-bind="item"
        ></XelFormItem>
      </el-form>
    </div>
    <template #display>
      <p v-html="$replaceToken(event.description)"></p>
    </template>
  </MouseDisplay>
  <MouseDisplay
    :play-name="'事件结论'"
    :show-submit="true"
    :disaboy="!eventBtns.eventDepiction"
    @submit="saveBaseInfo2"
    @close="eventClosed"
  >
    <div class="mt20">
      <el-form ref="consciouRef" :model="eventData" label-width="0" size="mini">
        <XelFormItem
          v-for="(item, index) in eventsness"
          :key="index"
          v-model="eventData[item.prop]"
          v-bind="item"
        ></XelFormItem>
      </el-form>
    </div>
    <template #display>
      <p>{{ event.eventDesc }}</p>
    </template>
  </MouseDisplay>
  <template
    v-if="
      eventStore.eventDetail.event && eventStore.eventDetail.event.isClose == 1
    "
  >
    <div class="title-bottom-line">
      <p>关闭原因</p>
    </div>
    <p class="onp">
      {{ eventStore.eventDetail.event.closeReason }}
    </p>
  </template>
  <div class="title-bottom-line">
    <p>事件升级/变更过程</p>
  </div>
  <p class="onp">{{ changeProcess }}</p>
  <!-- 关联事件 -->
  <div class="title-bottom-line">
    <p>关联事件</p>
  </div>
  <XelTable
    ref="tableRef12"
    :columns="listColumns"
    :data="associated"
    :pagination="false"
    @selection-change="handleSelectionChange"
  >
    <!-- <template #title="{ row }"> Case #{{ row.eventNo }}-{{ row.titlePrefix }} &nbsp;&nbsp;{{ row.title }} </template> -->
  </XelTable>
  <XelPagination
    ref="paginationRef"
    :init="false"
    class="xel-table-pagination pull-right"
    :total="total"
    @change="changePagination"
  ></XelPagination>
  <div class="clearfix"></div>
  <!-- 附件 -->
  <div class="title-bottom-line">
    <p>附件</p>
  </div>
  <SelTable
    ref="tableRef"
    :columns="columns"
    :load-data="getGetEventFilePage"
    :page-size="5"
    res-key="rows"
    :default-params="{
      id: route.params.id,
    }"
    :page-sizes="[5, 10, 20, 50]"
    :pagination="false"
    @selection-change="handleSelectionChange"
    :handle-data="(rows) => handleData(rows)"
  >
  </SelTable>
  <div class="title-bot"></div>
  <div class="title-bottom-line">
    <p>告警与报告</p>
    <!-- <el-button type="primary" :icon="Edit"></el-button> -->
  </div>
  <XelTable
    ref="tableRef1"
    :columns="onColumns"
    :load-data="selectAlarmReportPage"
    :page-size="5"
    :default-params="{
      eventId: route.params.id,
    }"
    :pagination="false"
    @selection-change="handleSelectionChange"
    :handle-data="(rows) => handleData(rows)"
  ></XelTable>

  <!-- 关联事件弹框 -->
  <XelDialog
    ref="dialogRef"
    title="关联要素"
    size="small"
    width="1200px"
    @submit="submitForm"
    @close="closeDialog"
  >
    <div v-if="assetsList.length > 0 && assetsList[0] !== null">
      <div class="title-bottom-line">
        <p>受影响资产</p>
      </div>
      <XelTable
        ref="tableRef6"
        :columns="loopholeColumns"
        :data="assetsList"
        :pagination="false"
      ></XelTable>
    </div>
    <div v-if="objectList.length > 0 && objectList[0] !== null">
      <div class="title-bottom-line">
        <p>可疑对象</p>
      </div>
      <XelTable
        ref="tableRef6"
        :columns="eventColumns"
        :data="objectList"
        :pagination="false"
      ></XelTable>
    </div>
    <div v-if="vulnList.length > 0 && vulnList[0] !== null">
      <div class="title-bottom-line">
        <p>漏洞</p>
      </div>
      <SelTable
        ref="tableRef7"
        :columns="earlyColumns"
        :data="vulnList"
        :pagination="false"
      ></SelTable>
    </div>
    <template #button>
      <el-button @click="closeValue">关闭</el-button>
    </template>
  </XelDialog>
</template>
<script setup>
import download from "@/utils/download";
import { downloadUrl } from "@/api/system/download.js";
import {
  getEventdetailmain,
  postSaveEditEvent,
  getGetEventFilePage,
  getGetEventRelevanceList,
  getGetRelativeList,
} from "@/api/event/event";
import { selectAlarmReportPage } from "@/api/event/alarmReport.js";
import { getDeptParentId } from "@/api/event/event";
import MouseDisplay from "./mouseDisplay.vue"; //鼠标移入编辑组件
import EditComponent from "./editComponent.vue";
import { loopholeColumns, eventColumns, earlyColumns } from "./relatedElements";
import updateByTabName from "../mixins/updateByTabName";
import { LEVEL_DATA } from "@/config/constant";
import useEventStore from "@/store/modules/eventDetail";
import { downloadFileById, getGetFileInfo } from "@/config/constant";
const router = useRouter();
const route = useRoute();

const event = ref({});
const baseInfoRef = ref();
const changeProcess = ref("");
const oneventDesc = ref("");
const tableRef1 = ref();
const tableRef = ref();
const associated = ref([]);
const dialogRef = ref();

const eventStore = useEventStore();
const total = ref("");

updateByTabName(0, () => {
  tableValue();
  eventDetail();
  search();
});
const eventBtns = computed(() => {
  return eventStore.overviewBtns;
});

const ass = ref({});
//获取事件详情
function eventDetail() {
  if (!router.name == "EventDetail") return;
  getEventdetailmain({ id: route.params.id }).then((res) => {
    eventStore.revisionOverview({
      eventUpdate: res.eventUpdate == "Y", //摘要,
      eventDepiction: res.eventDescription == "Y", //描述和结论
    });
    eventStore.updateEventFlag();

    oneventDesc.value = res.eventDesc;
    changeProcess.value = res.changeProcess;
    consciousness[0].editorData = consciouData.value.emailDetail =
      res.event.description;

    event.value = res.event;
    let ids = event.value.eventDeptList
      .map((item) => {
        return item.deptId;
      })
      .join(",");
    getDeptParentId({ ids: ids }).then((res) => {
      event.value.parentDeptName = res.map((item) => item.deptName).join(",");
    });
    event.value.findTime = [event.value.beginTimeStr, event.value.endTimeStr];
    eventData.value.eventDesc = res.event.eventDesc;
    ass.value = {
      id: route.params.id,
      isClose: res.event.isClose,
      pageNum: 1,
      pageSize: 10,
    };

    getList(ass.value);

    // getGetEventRelevanceList(ass.value).then((res) => {
    //   associated.value = res.data.rows;
    //   total.value = res.data.total;
    // });
  });
}
function getList(data) {
  getGetEventRelevanceList(data).then((res) => {
    associated.value = res.rows;
    total.value = res.total;
  });
}
// eventDetail(false);
const consciousness = reactive([
  {
    formType: "editor",
    prop: "emailDetail",
    label: "",

    editorClass: "formEditor", //多个编辑器时，命名不同的名字
    editorData: "",
    isClear: false,
    onEditorValue(val) {
      consciouData.value.emailDetail = val;
    },
  },
]);
const consciouData = ref({
  emailDetail: "",
});
const eventData = ref({
  eventDesc: "",
});
const eventsness = reactive([
  {
    formType: "input",
    label: "",
    size: "mini",
    // required: true,
    prop: "eventDesc",
    type: "textarea",
    // placeholder:"请输入事件结论"
  },
]);
// 修改
function saveBaseInfo(a, b) {
  const ass = [];
  baseInfoRef.value.formData.eventTagList.forEach((item) => {
    ass.push(item.id);
  });
  const val = {
    title: baseInfoRef.value.formData.title,
    levelId: baseInfoRef.value.formData.levelId,
    beginTimeStr: baseInfoRef.value.formData.beginTimeStr,
    endTimeStr: baseInfoRef.value.formData.endTimeStr,
    deptId: baseInfoRef.value.formData.eventDeptList.toString(),
    tagId: ass.toString(),
    id: route.params.id,
    spare1: 1,
  };
  b();
  postSaveEditEvent(val)
    .then((res) => {
      ElMessage.success("保存成功");

      eventDetail();
      a();
    })
    .catch(() => {
      a(false);
    });
}
const columns = [
  {
    prop: "fileName",
    label: "文件名",
  },
  {
    prop: "createTime",
    label: "时间",
  },
  {
    prop: "type",
    label: "文件类型",
  },
  {
    prop: "size",
    label: "文件大小",
  },
  {
    slotName: "action",
    btnList: [
      {
        icon: "download-outline",
        title: "下载",
        onClick(scope) {
          downloadFileById(scope.row.fileId);
        },
      },
    ],
  },
];
const onColumns = [
  {
    prop: "name",
    label: "告警与报告名称",
  },
  {
    prop: "fileName",
    label: "文件名",
  },
  {
    prop: "createTime",
    label: "提交时间",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "download-outline",
        title: "下载",
        onClick(scope) {
          downloadFileById(scope.row.fileId);
        },
      },
    ],
  },
];
const listColumns = [
  {
    prop: "titlePrefix",
    label: "事件类型",
  },
  {
    prop: "titlePrefix",
    label: "事件编号与名称",
    formatter(row, column) {
      return row.eventNo + "-" + row.title;
    },
  },
  {
    prop: "closeStr",
    label: "分析进度",
  },
  {
    prop: "relevance",
    label: "关联要素",
    click(scope) {
      relatedElements(scope.row.id);
    },
  },
  {
    prop: "createTime",
    label: "时间",
  },
];
//搜索相关
const searchState = reactive({
  data: {
    fileName: "",
  },
  menuData: [],
  formList: [
    {
      formType: "input",
      prop: "fileName",
      label: "文件名",
    },
  ],
});
function search(initPageNum = true) {
  tableRef.value.reload(searchState.data, initPageNum);
}
function tableValue() {
  tableRef1.value.reload(true);
}
function reset() {
  searchState.data = {
    fileName: "",
  };
  search();
}
// 附件表格数据处理
// 返回文件信息
function getFileName(fileId) {
  let fileData = getGetFileInfo({ fileId: fileId }).then(async (res) => {
    return {
      fileName: res.fileName,
      type: res.type,
      size: (res.size / 1000).toFixed("1"),
      createTime: res.createTime,
    };
  });
  return fileData;
}
function handleData(rows) {
  rows.forEach(async (item) => {
    let fileData = await getFileName(item.fileId);

    item.fileName = fileData.fileName;
    item.type = fileData.type;
    item.size = fileData.size;
    item.createTime = fileData.createTime;
  });
  rows.map((rItem) => {
    return {
      ...rItem,
    };
  });

  return rows;
}

// 修改描述
function saveBaseInfo1(a, b) {
  const add = [];
  event.value.eventTagList.forEach((item) => {
    add.push(item.tagId);
  });
  const ass = {
    id: route.params.id,
    description: consciouData.value.emailDetail,
    // 标题
    title: event.value.title,
    // 等级ID
    levelId: event.value.levelId,
    // 开始时间
    beginTimeStr: event.value.beaginTime,
    // 结束时间
    endTimeStr: event.value.endTime,
    // 部门id
    deptId: event.value.deptId,
    // 标签id
    tagId: add.toString(),
    spare1: 2,
  };
  b();
  postSaveEditEvent(ass)
    .then((res) => {
      ElMessage.success("保存成功");
      eventDetail();
      a();
    })
    .catch(() => {
      a(false);
    });
}
// 修改结论
function saveBaseInfo2(a, b) {
  const add = [];
  event.value.eventTagList.forEach((item) => {
    add.push(item.tagId);
  });
  const ass = {
    id: route.params.id,
    eventDesc: eventData.value.eventDesc,
    // 标题
    title: event.value.title,
    // 等级ID
    levelId: event.value.levelId,
    // 开始时间
    beginTimeStr: event.value.beaginTime,
    // 结束时间
    endTimeStr: event.value.endTime,
    // 部门id
    deptId: event.value.deptId,
    // 标签id
    tagId: add.toString(),
    spare1: 3,
  };
  b();
  postSaveEditEvent(ass)
    .then((res) => {
      ElMessage.success("保存成功");
      eventDetail();

      a();
    })
    .catch(() => {
      a(false);
    });
}
// 关联要素
const assetsList = ref([]);
const objectList = ref([]);
const vulnList = ref([]);
function relatedElements(val) {
  const ass = {
    id: val,
    eventId: route.params.id,
  };
  getGetRelativeList(ass).then((res) => {
    assetsList.value = res.assetsList;
    objectList.value = res.objectList;
    vulnList.value = res.vulnList;
  });
  dialogRef.value.open();
}
// 分页
function changePagination(val) {
  ass.value.pageNum = val.pageNum;
  ass.value.pageSize = val.pageSize;
  getList(ass.value);
}
// 关闭按钮
function closeValue() {
  dialogRef.value.close();
}
// 关闭方法
function closeDialog() {
  assetsList.value = [];
  objectList.value = [];
  vulnList.value = [];
}
function eventClosed() {
  if (event.value.eventDesc) {
    eventData.value.eventDesc = event.value.eventDesc;
  } else {
    eventData.value.eventDesc = "";
  }
}
</script>

<style lang="scss" scoped>
.base-info-form {
  :deep(.el-form-item) {
    border-bottom: 1px solid #ebedf1;
    margin-bottom: 10px;
    padding-bottom: 10px;
  }
}
.el-tag {
  margin-right: 10px;
}
.onp {
  margin: 15px 0 40px 0;
}
.title-bottom-line {
  display: flex;
  justify-content: space-between;
}
.dobule {
  display: inline-block;
  max-width: 300px;
  white-space: normal;
}
.title-bot {
  margin: 20px 0 20px 0;
}
.title-add {
  margin-right: 5px;
}
:deep .home {
  width: 100%;
}
</style>
