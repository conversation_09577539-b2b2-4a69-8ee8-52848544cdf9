import form from "@/customComponents/formCreate/designer/config/base/form";
import router from "@/router";
let loopholeColumns = [
  {
    prop: "name",
    label: "资产名称",
    clickDept: "eventTitle",
  },
  {
    prop: "internetIp",
    label: "IP",
  },
  {
    prop: "domain",
    label: "业务入口",
  },
];
let eventColumns = [
  {
    prop: "type",
    label: "类型",
  },
  {
    prop: "content",
    label: "值/文件名",
  },
];
let earlyColumns = [
  {
    prop: "title",
    label: "漏洞标题",
  },
  {
    prop: "type",
    label: "漏洞类型",
  },
  {
    prop: "level",
    isLevel: true,
    label: "漏洞等级",
    propName: "levelName",
  },
  {
    prop: "findTimeStr",
    label: "漏洞时间",
  },
];

export { loopholeColumns, eventColumns, earlyColumns };
