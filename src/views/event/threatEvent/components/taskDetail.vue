<template>
  <div v-if="pageStatus" id="taskDetail">
    <!-- v-if="btnList.filter((item) => !item.hide).length > 0 " -->
    <div class="handler-btns">
      <AlertSearchBtn
        :isBtnEmits="isSearch"
        :isSearch="false"
        @click="searchBtn"
      />

      <el-button
        v-for="btn in btnList.filter((item) => !item.hide)"
        :key="btn.icon"
        size="mini"
        @click="btn.onClick"
      >
        <el-icon v-if="btn.icon">
          <Component :is="btn.icon" />
        </el-icon>
        <el-icon v-else>
          <svg class="icon" aria-hidden="true">
            <use :xlink:href="'#' + btn.isFont"></use>
          </svg>
        </el-icon>
        {{ btn.title }}
      </el-button>
    </div>
    <MouseDisplay
      :play-name="'基础信息'"
      :show-submit="true"
      :disaboy="!isEventTab || taskDetail.canUpdate != 'Y'"
      @close="echoDataBase(taskDetailState.eventTask)"
      @submit="saveBaseInfo"
    >
      <el-form
        ref="baseFormRef"
        :model="baseForm"
        label-width="120px"
        label-position="left"
        class="base-info-form"
      >
        <XelFormItem
          v-for="(item, index) in baseFormList"
          :key="index"
          v-model="baseForm[item.prop]"
          v-bind="item"
        ></XelFormItem>
      </el-form>
      <template #display>
        <el-form
          label-width="120px"
          label-position="left"
          class="base-info-form"
        >
          <el-row>
            <el-col :span="12">
              <el-form-item label="标题：">
                {{ taskDetailState.eventTask.title }}</el-form-item
              >
              <el-form-item label="阶段："
                >{{ taskDetailState.eventTask.taskGroupText }}
              </el-form-item>
              <el-form-item label="分析师："
                >{{ taskDetailState.eventTask.assigneeName }}
              </el-form-item>
              <el-form-item
                v-if="isEventTab && Number(eventTask.auditStatus) === 0"
                label="分析角色："
                >{{ taskDetailState.eventTask.assigneeRoleName }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="执行时间："
                >{{ taskDetailState.eventTask.startTime }}
              </el-form-item>
              <el-form-item label="完成时间："
                >{{ taskDetailState.eventTask.endTime }}
              </el-form-item>
              <el-form-item label="历时："
                >{{ minToDate(taskDetailState.eventTask.takeUpTime) }}
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </template>
    </MouseDisplay>
    <MouseDisplay
      :play-name="'任务要求'"
      :show-submit="true"
      :disaboy="!isEventTab || taskDetail.canUpdate != 'Y'"
      @submit="saveDetail"
      @close="closedesc"
    >
      <XelEdit
        class="mb10"
        editor-class="detailEditor"
        :desc="detailEdit"
        @change="detailEdit = $event"
      ></XelEdit>
      <template #display>
        <div
          class="evetTaskD"
          v-html="$replaceToken(taskDetailState.eventTask.description)"
        ></div>
      </template>
    </MouseDisplay>
    <!-- 动态表单 -->
    <MouseDisplay
      v-if="designerRule.length > 0"
      :play-name="'动态表单'"
      :show-submit="true"
      :disaboy="!isEventTab || taskDetail.canUpdateLog != 'Y'"
      @submit="saveFormCreate"
      @close="closeFormCreate"
    >
      <el-form
        ref="dynamicFormRef"
        :model="formData"
        size="medium"
        label-width="100px"
      >
        <FormCreate
          v-model="formData"
          class="dynamic"
          :rule="editDesignerRule"
          :option="editDesignerOption"
        ></FormCreate>
      </el-form>
      <template #display>
        <form-create
          :rule="designerRule"
          :option="designerOption"
        ></form-create>
      </template>
    </MouseDisplay>
    <div>
      <div class="title-bottom-line mt20">
        <p>执行日志1</p>
      </div>
      <div class="taskLogList">
        <!-- 添加日志 -->
        <el-button
          v-if="taskDetail.canUpdateLog == 'Y'"
          type="primary"
          style="margin-bottom: 10px"
          @click="openLogDialog"
          >添加日志</el-button
        >
        <LogGroup
          :list="taskDetailState.logList"
          :show-edit="isEventTab"
          :can-update-log="isEventTab && taskDetail.canUpdateLog == 'Y'"
          @delFile="delFile"
          @openEditLog="openEditLog"
          @delLog="delLog"
        ></LogGroup>
      </div>
    </div>
    <div>
      <div class="title-bottom-line mt20">
        <p>相关原始日志</p>
      </div>
      <SiemLogGroup
        v-if="taskDetailState.eventTask"
        :can-update-log="isEventTab && taskDetail.canUpdateLog == 'Y'"
        :event-task-id="props.samplingStatus.taskId"
      ></SiemLogGroup>
    </div>

    <div v-if="taskDetailState.auditList.length > 0">
      <div class="title-bottom-line mt20">
        <p>审核结果</p>
      </div>
      <div class="taskLogList">
        <el-collapse accordion>
          <el-collapse-item
            v-for="item in taskDetailState.auditList"
            :key="item.id"
            :name="item.id"
          >
            <template #title>
              <p class="logItem">
                <span class="titleName">
                  <span>
                    <SvgIcon
                      class="icon"
                      :size="16"
                      icon-class="person-done-outline"
                    />
                  </span>
                  <span>审核者：</span>
                  <span>{{ item.auditName }}</span>
                </span>
                <span class="titleTime">
                  <span>
                    <SvgIcon
                      class="icon"
                      :size="16"
                      icon-class="clock-outline"
                    />
                  </span>
                  <span>审核日期：</span>
                  <span>{{ item.auditTime }}</span>
                </span>
                <span>
                  <span></span>
                  <span>激励加分：</span>
                  <span class="reward">{{ item.reward }}</span>
                </span>
              </p>
            </template>
            <div>
              <p>
                <span
                  v-if="item.status === 1"
                  style="color: var(--primary-green-color)"
                  >审核通过</span
                >
                <span v-else style="color: var(--primary-red-color)">驳回</span>
              </p>
              <p class="auditDetail">
                <span>审核意见：</span>
                <span>{{ item.comment }}</span>
              </p>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
    <div
      v-if="
        (!isEventTab && samplingStatus.samplingStatus == '0') || auditShowStatus
      "
      class="mt20 audit"
    >
      <div class="title-bottom-line">
        <p>审核</p>
      </div>
      <div>
        <el-form ref="ruleFormRef" label-width="120px" size="mini">
          <el-form-item label="审核结果：">
            <el-radio-group
              v-model="auditState.data.status"
              @change="chageAduitStatus"
            >
              <el-radio label="1" style="margin-top: 12px">审核通过</el-radio>
              <!-- <el-radio v-if="" v-model="auditState.data.status" label="0">任务质量驳回</el-radio> -->
              <el-radio label="0" style="margin-top: 12px">{{
                taskDetailState.isAlert === "Y" ? "任务质量驳回" : "驳回"
              }}</el-radio>
              <el-radio v-if="taskDetailState.isAlert === 'Y'" label="2"
                >误报</el-radio
              >
              <el-radio v-if="taskDetailState.isAlert === 'Y'" label="3"
                >提报新事件</el-radio
              >
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="auditState.data.status == '1'" label="激励加分：">
            <el-input
              v-model="auditState.data.reward"
              type="number"
              placeholder="请输入分数"
              style="width: 200px"
              @input="changeReward"
            ></el-input>
          </el-form-item>
          <el-form-item label="审核意见：">
            <el-input
              v-model="auditState.data.comment"
              type="textarea"
              placeholder="请输入审核意见"
              rows="5"
              maxlength="300"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="text-center mt30">
      <el-button v-if="!isEventTab" type="button" @click="closeAudit"
        >取消</el-button
      >
      <el-button
        v-if="
          (!isEventTab && samplingStatus.samplingStatus == '0') ||
          auditShowStatus
        "
        type="primary"
        @click="saveAudit"
        >确定</el-button
      >
    </div>
    <!-- 添加，编辑日志弹框 -->
    <XelDialog
      ref="logDialogRef"
      :title="`${activeLog.id ? '编辑' : '添加'}日志`"
      @submit="submitLog"
      @close="closeLogDialog"
    >
      <el-form ref="logFormRef" :model="logFormData">
        <SelFormItem
          v-for="item in logFormList"
          :key="item.prop"
          v-model="logFormData[item.prop]"
          v-bind="item"
        ></SelFormItem>
      </el-form>
    </XelDialog>
    <!-- 提报事件 -->
    <XelDialog
      ref="choseEventTemplate"
      :title="eventDialogTitle"
      :ishidden-dialog="true"
      width="60%"
      @close="clsoeCreateEventDialog"
      @toParentDialogTitle="getDialogTitle"
    >
      <AddEvent
        v-if="addEventShow"
        :alert-add-event="createEventState.data"
        add-type="WorkbenchAlert"
      ></AddEvent>
    </XelDialog>
    <!-- 告警详情  -->
    <xel-dialog
      title="告警任务信息"
      ref="detailsRef"
      width="1200px"
      :show-submit="false"
      buttonCancel="关闭"
    >
      <AlertTable />
    </xel-dialog>
  </div>
</template>

<script setup>
import { minToDate } from "@/utils/minToDate";
import { getDicts } from "@/api/system/dict/data";
import AlertTable from "./alertTable.vue";

// 执行日志 审核结果列表
import {
  getLogList,
  getAuditList,
  viewEventTaskDetail,
  updateEventTask,
  saveTaskLog,
  getDisplayInfo,
  getDisplayData,
  saveDisplayData,
  putDisplayInfo,
} from "@/api/event/task";
import { postRemoveFile, postDeleteTaskLog } from "@/api/event/eventTask";
import { getAssigneeRoles, selectUserTree } from "@/api/event/eventList";
import AddEvent from "@/views/event/threatEvent/components/addEvent.vue";
import MouseDisplay from "./mouseDisplay.vue"; //鼠标移入编辑组件
import LogGroup from "./logGroup.vue";
import AlertSearchBtn from "@/views/workSpace/components/alertSearchBtn.vue";
import { selectEventAlertPage } from "@/api/event/alarmReport.js";
import taskHandle from "../mixins/taskHandle";
import useUserStore from "@/store/modules/user";
import useEventStore from "@/store/modules/eventDetail";
import { getGetFileInfo } from "@/config/constant";
import SiemLogGroup from "./siemLogGroup.vue";

const { handlerTask, saveAuditFn } = taskHandle();

const userStore = useUserStore();
const eventStore = useEventStore();
const route = useRoute();

//更新页面
const taskUpdate = computed(() => {
  return eventStore.taskUpdate;
});
const pageStatus = ref(true);
watch(
  () => taskUpdate.value.status,
  () => {
    if (
      taskUpdate.value.id == taskDetailState.eventTask.id ||
      taskUpdate.value.id == route.params.id
    ) {
      pageStatus.value = false;
      getTaskDetail();

      setTimeout(() => {
        pageStatus.value = true;
      }, 200);
    }
  }
);

const props = defineProps({
  //是否是事件详情中的任务详情tab
  isEventTab: {
    type: Boolean,
    default: false,
  },
  samplingStatus: {
    type: Object,
    default: () => {
      return {};
    },
  },
  isManageTask: {
    type: [String, Number],
    default: "",
  }, //0 执行 1处置
  audit: {
    type: Boolean,
    default: false,
  },
  reAudit: {
    type: Boolean,
    default: false,
  },
});

const taskDetailState = reactive({
  taskDetail: {},
  eventTask: {},
  isAlert: "",
  eventAlert: {},
  logList: [],
  auditList: [],
});
/* 获取告警详情 */
let isSearch = ref(false);
const getEAlert = () => {
  selectEventAlertPage({ eventId: route.params.id }).then((res) => {
    isSearch.value = res.data.total > 0;
  });
};
getEAlert();
//打开告警详情
let detailsRef = ref();
const searchBtn = () => {
  detailsRef.value.open();
};
function chageAduitStatus(val) {
  auditState.data.comment = "";
  auditState.data.reward = 0;
}
const btnList = ref([]);
let isManageTask = false;
// 获取抽检事件中任务详情
function getTaskDetail(echoType) {
  viewEventTaskDetail(props.samplingStatus.taskId).then((res) => {
    const resData = res;

    taskDetailState.taskDetail = res;
    eventStore.eventDeptList = res.eventTask.deptList.map(
      (item) => item.deptId
    );
    if (!echoType) {
      taskDetailState.eventTask = resData.eventTask;
      //回显表单信息 审核状态==0的时候有执行人和执行角色的编辑权限
      echoDataBase(resData.eventTask);
      echoDataDetail(resData.eventTask);

      taskDetailState.isAlert = resData.isAlert;
      taskDetailState.eventAlert = resData.eventAlert;

      btnList.value = getBtnList(resData.eventTask);
    } else {
      switch (echoType) {
        case "base":
          echoDataBase(resData.eventTask);
          break;
        case "detail":
          echoDataDetail(resData.eventTask);
          break;
      }
    }

    isManageTask = resData.eventTask.isManageTask;
    // 事件处置 事件分析 任务阶段截取
    if ("isManageTask" in resData.eventTask) {
      getDicts("task_group").then((res) => {
        let options = res.data.map((item) => {
          return {
            value: item.dictValue,
            label: item.dictLabel,
          };
        });
        baseFormList.value[1].options =
          resData.eventTask.isManageTask == 0
            ? options.slice(0, 3)
            : options.slice(3);
      });
    }
    getUserTree();
    getRoleTree();
  });
  getLogListFn();
  // 获取审核结果列表
  getAuditList(props.samplingStatus.taskId).then((res) => {
    //
    taskDetailState.auditList = res.data;
  });
}
function getLogListFn() {
  // 获取执行日志
  getLogList({ eventTaskId: props.samplingStatus.taskId }).then((res) => {
    taskDetailState.logList = res.rows;
    taskDetailState.logList.forEach((item) => {
      item.eventTaskLogFile.forEach(async (eItem) => {
        let fileData = await getFileInfo(eItem.fileId);
        eItem.fileId = fileData.fileId;
        eItem.fileName = fileData.fileName;
        eItem.type = fileData.type;
        eItem.size = fileData.size;
      });
    });
  });
}
function getFileInfo(fileId) {
  let fileData = getGetFileInfo({ fileId: fileId }).then(async (res) => {
    return {
      fileId: res.fileId,
      fileName: res.fileName,
      type: res.type,
      size: (res.size / 1000).toFixed("1"),
    };
  });
  return fileData;
}

getTaskDetail();

// 任务审核
const auditState = reactive({
  data: {
    eventTaskId: props.samplingStatus.taskId,
    status: "1",
    reward: null,
    comment: "",
    samplingRecordId: props.samplingStatus.id,
  },
});
function changeReward(value) {
  auditState.data.reward = parseInt(value);
  if (value > 10000 || value < -10000) {
    ElMessage.warning("激励加分请输入-10000 到 10000之间的整数");
    auditState.data.reward = null;
  }
}
// 三线工作台 抽检事件审核任务
const emits = defineEmits(["close"]);
// 取消
function closeAudit() {
  emits("close");
}
// 保存审核内容
// 提报事件传值内容
const createEventState = reactive({
  data: {},
});
const choseEventTemplate = ref();
const addEventShow = ref(false);
function clsoeCreateEventDialog() {
  addEventShow.value = false;
  createEventState.data = {};
}
const eventDialogTitle = ref("选择事件模板");

function getDialogTitle(title) {
  eventDialogTitle.value = title;
}
function saveAudit() {
  const status = auditState.data.status;

  if (status === "1") {
    if (!auditState.data.reward && auditState.data.reward != 0) {
      ElMessage.warning("激励加分请输入-10000 到 10000之间的整数");
      return false;
    }
  }
  if (status === "3") {
    createEventState.data = {
      alertId: taskDetailState.eventAlert.alertId,
      sourceId: taskDetailState.eventAlert.sourceId,
      firstTaskId: taskDetailState.eventTask.id,
      samplingRecordId: props.samplingStatus.id,
      csrcip: taskDetailState.eventAlert.csrcip,
      cdstip: taskDetailState.eventAlert.cdstip,
      handleFrom: "audit",
    };
    addEventShow.value = true;
    choseEventTemplate.value.open();
  } else {
    if (
      status === "2" &&
      (auditState.data.comment === "" || auditState.data.comment == null)
    ) {
      ElMessage.warning("请输入审核意见");
      return false;
    }
    if (props.isEventTab) {
      saveAuditFn(
        {
          eventTaskId: props.samplingStatus.taskId,
          status: auditState.data.status,
          reward: auditState.data.reward,
          comment: auditState.data.comment,
          auditType: auditType,
        },
        eventStore,
        () => {
          auditShowStatus.value = false;
          // 误报 关闭任务详情
          if (auditState.data.status == 2) {
            eventStore.delTabByName(props.samplingStatus.taskId);
          } else {
            eventStore.updateEventActiveTab({ audit: false, reAudit: false });
          }
        }
      );
    }
  }
}

//事件任务
//保存基本信息
const baseFormRef = ref();
const baseForm = ref({
  title: "",
  taskGroup: "",
  assignee: "",
  assigneeRole: [],
});

const roleTreeData = [];
const userTreeData = [];

const baseFormList = ref([
  {
    formType: "input",
    prop: "title",
    label: "标题",
    required: true,
  },
  {
    formType: "select",
    prop: "taskGroup",
    label: "阶段",
    required: true,
    options: [],
  },
  {
    formType: "tree",
    prop: "assignee",
    label: "执行人",
    multiple: true,
    treeData: userTreeData,
    disabledKey: "value",
  },
  {
    formType: "tree",
    prop: "assigneeRole",
    label: "执行角色",
    multiple: true,
    treeData: roleTreeData,
  },
]);

let userPerm = false;
function echoDataBase(data) {
  userPerm = Number(data.auditStatus) === 0;
  baseFormList.value[2].isShow = baseFormList.value[3].isShow = userPerm;

  if (userStore.roles.includes("one")) {
    baseFormList.value[2].isShow = baseFormList.value[3].isShow = false;
  }
  baseForm.value.title = data.title;
  baseForm.value.taskGroup = data.taskGroup;
  //任务未开始前
  if (data.auditStatus == 0) {
    baseForm.value.assignee = data.spare3 ? data.spare3.split(",") : [];
  } else {
    baseForm.value.assignee = data.assignee ? data.assignee.split(",") : [];
  }

  baseForm.value.assigneeRole = data.assigneeRole
    ? data.assigneeRole.split(",")
    : [];

  [
    "title",
    "taskGroupText",
    "assigneeName",
    "assigneeRoleName",
    "startTimeStr",
    "endTimeStr",
    "takeUpTime",
  ].forEach((key) => {
    taskDetailState.eventTask[key] = data[key];
  });
}

function getUserTree() {
  let params = {
    deptIds:
      eventStore.eventDeptList.length > 0 && eventStore.eventDeptList.join(","),
  };
  selectUserTree(params).then((res) => {
    baseFormList.value[2].treeData = res.data.data;
  });
}
function getRoleTree() {
  let params = {
    deptIds:
      eventStore.eventDeptList.length > 0 && eventStore.eventDeptList.join(","),
  };
  getAssigneeRoles(params).then((res) => {
    baseFormList.value[3].treeData = res.data.data.map((item) => {
      return {
        id: item.roleId,
        label: item.roleName,
      };
    });
  });
}
function saveBaseInfo(close, showLoad) {
  baseFormRef.value.validate((valid) => {
    if (valid) {
      const params = {
        ...taskDetailState.eventTask,
        ...baseForm.value,
        assignee: baseForm.value.assignee.join(),
        assigneeRole: baseForm.value.assigneeRole.join(),
      };
      //不可编辑执行人的任务不传assignee

      if (!userPerm) {
        delete params.assignee;
      }
      delete params.description;
      saveBaseInfoFn(params, "base", close, showLoad);
    }
  });
}
function saveBaseInfoFn(params, type, close, showLoad) {
  showLoad();

  updateEventTask(params)
    .then(() => {
      ElMessage.success("操作成功");
      getTaskDetail(type);
      close();
      eventStore.updateTaskById(props.samplingStatus.taskId);
      eventStore.updateEventActiveTab({ label: params.title });
    })
    .catch(() => {
      close(false);
    });
}

//任务要求
const detailEdit = ref("");
function saveDetail(close, showLoad) {
  if (!detailEdit.value) {
    ElMessage.warning("请输入任务要求");
    return;
  }
  const params = {
    ...taskDetailState.eventTask,

    description: detailEdit.value,
  };

  delete params.assignee;

  saveBaseInfoFn(params, "description", close, showLoad);
}

function closedesc() {
  detailEdit.value = taskDetailState.eventTask.description;
}

function echoDataDetail(data) {
  detailEdit.value = data.description;
  taskDetailState.eventTask.description = data.description;
}

//添加，编辑日志
const logDialogRef = ref();
const logFormRef = ref();
const logFormData = ref({
  content: "",
  fileIds: "",
});
const logFileList = ref([]);
const logDialogStatus = ref(false);

const logFormList = ref([
  {
    is: "editor",
    prop: "content",
    option: {},
    label: "日志内容",
    span: 24,
    required: true,
  },
  {
    is: "upload",
    prop: "fileIds",
    option: {
      data: {
        deptId: "1",
      },
      fileType: ["doc", "docx", "xls", "xlsx", "ppt", "txt", "pdf"],
      limit: 100,
    },
    label: "上传文件",
  },
]);
const activeLog = ref({});

function openLogDialog() {
  activeLog.value = {};
  logFormData.value = {
    content: "",
    fileIds: "",
  };

  logFileList.value = [];

  logDialogRef.value.open(() => {
    nextTick(() => {
      logFormList.value[0].isClear = !logFormList.value[0].isClear;
      logFormList.value[0].editorData = "";
    });
  });
}
function openEditLog(log) {
  activeLog.value = JSON.parse(JSON.stringify(log));
  logFormData.value.content = log.content;
  logFileList.value = [];
  logDialogRef.value.open(() => {
    nextTick(() => {
      logFormList.value[0].editorData = log.content;
    });
  });
}
function closeLogDialog() {
  logFormList.value[1].fileListFa = [];
}
function submitLog(close, load) {
  logFormRef.value.validate((valid) => {
    if (valid) {
      load();
      let params = {
        id: activeLog.value.id ? activeLog.value.id : "",
        eventTaskId: props.samplingStatus.taskId,
        ...logFormData.value,
      };

      saveTaskLog(params)
        .then(() => {
          ElMessage.success("操作成功");
          getLogListFn();
          close();
          logDialogStatus.value = false;
        })
        .catch(() => {
          close(false);
          logDialogStatus.value = false;
        });
    }
  });
}

function delLog(item) {
  ElMessageBox.confirm(`确认删除日志？`, "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    postDeleteTaskLog({
      id: item.id,
      eventTaskId: props.samplingStatus.taskId,
    }).then(() => {
      ElMessage.success("删除成功");
      getLogListFn();
    });
  });
}

//删除日志文件
function delFile(item) {
  postRemoveFile({
    eventTaskId: props.samplingStatus.taskId,
    fileId: item.fileId,
    fileName: item.fileName,
  }).then((res) => {
    ElMessage.success("删除成功");
    getLogListFn();
  });
}

//操作按钮
function getBtnList(row) {
  return [
    {
      hide: row.startFlag != "Y",
      icon: "VideoPlay",
      title: "开始",
      onClick(scope) {
        handlerTask("start", row, isManageTask, false, eventStore);
      },
    },
    {
      hide: row.submitFlag != "Y",

      icon: "Upload",
      title: "提报",
      onClick(scope) {
        handlerTask("submit", row, isManageTask, false, eventStore);
      },
    },
    {
      hide: row.deleteFalg != "Y",
      icon: "Delete",
      title: "删除",
      onClick(scope) {
        handlerTask("del", row, isManageTask, false, eventStore);
      },
    },
    {
      hide: row.reAuditFlag != "Y",
      icon: "RefreshRight",
      title: "重审",
      onClick(scope) {
        showAudit("reAudit");
      },
    },
    {
      hide: row.closeFlag != "Y",
      icon: "CircleClose",
      title: "关闭",
      onClick(scope) {
        handlerTask("close", row, isManageTask, false, eventStore);
      },
    },
    {
      hide: row.redoFlag != "Y",
      icon: "Switch",
      title: "重做",
      onClick(scope) {
        handlerTask("redo", row, isManageTask, false, eventStore);
      },
    },
    {
      width: "300px",
      hide: row.auditFlag != "Y",
      icon: "Checked",
      title: "审核",
      onClick(scope) {
        showAudit("audit");
      },
    },
  ];
}
const auditShowStatus = ref(false);
function showAudit(type) {
  auditState.data.status = "1";
  auditState.data.reward = 0;
  auditState.data.comment = "";
  auditShowStatus.value = true;
  auditType = type;
  setTimeout(() => {
    document.getElementById("taskDetail").scrollIntoView(false);
  }, 200);
}
let auditType = "";
//是否展示审核
if (props.audit || props.reAudit) {
  showAudit();
  auditType = props.reAudit ? "reAudit" : "audit";
}

// 动态表单

const designerOption = ref([]);
const designerRule = ref([]);

const editDesignerRule = ref([]);
const editDesignerOption = ref([]);

let formCreateData = [];
getFormCreatFn();

function getFormCreatFn() {
  getDisplayInfo({ eventTaskId: props.samplingStatus.taskId }).then((res) => {
    if (res) {
      designerRule.value = JSON.parse(res.displayInfo);

      designerOption.value = JSON.parse(res.options);
      // 编辑动态表单
      editDesignerRule.value = JSON.parse(res.displayInfo);
      editDesignerOption.value = JSON.parse(res.options);
    }

    getDisplayData(props.samplingStatus.taskId).then(({ data }) => {
      formCreateData = data;

      designerRule.value.forEach((dItem) => {
        dItem["props"]["disabled"] = true;
      });

      data.forEach((dItem) => {
        designerRule.value.forEach((item) => {
          if (dItem.field == item.field) {
            item["value"] = dItem["value"];
            item["title"] = dItem["title"];
            if (item.type == "checkbox") {
              item["value"] = dItem["value"].split(",");
            }
          }
        });
        editDesignerRule.value.forEach((eItem) => {
          if (dItem.field == eItem.field) {
            eItem["value"] = dItem["value"];
            eItem["title"] = dItem["title"];
            if (eItem.type == "checkbox") {
              formData.value[eItem.field] = dItem["value"].split(",");

              item["value"] = dItem["value"].split(",");
            }
          }
        });
      });
    });
  });
}
let formData = ref({});
// 处理动态表单需要提交的数据
function handleSubFormData(data) {
  Object.keys(formData.value).forEach((kItem) => {
    data.forEach((dItem) => {
      if (dItem["field"] == kItem) {
        dItem["value"] = formData.value[kItem];
        dItem["eventTaskId"] = props.samplingStatus.taskId;
      }
    });
  });

  return data;
}
// 提交动态表单
function saveFormCreate(close, showLoad) {
  let params = [];
  params = handleSubFormData(editDesignerRule.value);

  if (formCreateData.length > 0) {
    params = handleSubFormData(formCreateData);
  }
  params.forEach((pItem) => {
    if (Array.isArray(pItem.value)) {
      if (pItem.value.length > 1) {
        pItem.value = pItem.value.join(",");
      } else {
        pItem.value = pItem.value.toString();
      }
    }
  });

  let api = formCreateData.length > 0 ? putDisplayInfo : saveDisplayData;

  api(params).then((res) => {
    close();
    getFormCreatFn();
  });
}
function closeFormCreate() {}

const { eventTask, isAlert, eventAlert, logList, auditList, taskDetail } =
  toRefs(taskDetailState);
</script>

<style lang="scss" scoped>
.base-info-form {
  :deep(.el-form-item) {
    border-bottom: 1px solid #ebedf1;
    // margin-top: 10px;
    padding-bottom: 10px;
    margin: 0px 20px 10px 20px;
  }
}
.evetTaskD {
  padding: 20px;
}
:deep(.el-collapse-item__header) {
  display: block;
  border-bottom: 1px solid #ebeef5;
  // background: var(--secondary-bg-color);
  .el-collapse-item__arrow {
    float: left;
    width: 30px;
    margin-top: 16px;
    text-align: center;
    &::after {
      clear: both;
      content: "";
    }
  }
  .logItem {
    float: right;
    text-align: left;
    width: calc(100% - 40px);
    & > span {
      font-size: 14px;
      display: inline-block;
      line-height: 48px;
      margin-right: 20px;
      // color:var( --text-color)Soft;
      & > span:nth-child(1) {
        font-size: 20px;
        line-height: 20px;
        float: left;
        margin-top: 13px;
      }
      & > span:nth-child(3) {
        color: #559cf6;
        &.reward {
          padding: 2px 7px;
          border-radius: 22px;
          background-color: #3498db;
          border-color: #3498db;
          color: #ffffff;
        }
      }
      & > span {
        margin-right: 5px;
      }
    }
  }
}
:deep(.el-collapse-item__wrap) {
  width: 100%;
}
:deep(.el-collapse-item__content) {
  padding: 10px 20px;
  .auditDetail {
    > span {
      float: left;
      &:nth-child(1) {
        width: 80px;
      }
      &:nth-child(2) {
        width: calc(100% - 80px);
      }
    }
  }
}
.audit {
  :deep(.el-radio.el-radio--mini) {
    margin-top: 0px !important             ;
  }
}
.handler-btns {
  text-align: right;
  padding-bottom: 10px;
  border-bottom: 1px dashed rgb(224, 221, 221);
  margin-bottom: 20px;
}
:deep(.btn-position) {
  position: inherit;
}
</style>
