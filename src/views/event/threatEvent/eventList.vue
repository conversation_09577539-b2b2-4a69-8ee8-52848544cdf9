<template>
  <Component
    :is="currentRouteName == 'EventList' ? ResizeWrapper : 'div'"
    :class="currentRouteName == 'EventList' ? 'tree-table-wrapper' : ''"
    ref="resizeRef"
  >
    <template #tree>
      <TreeCollection
        :types="[leftTreeType]"
        :spare1="4"
        @getId="getIdFn"
      ></TreeCollection>
    </template>
    <section>
      <EventTotal
        v-if="
          currentRouteName != 'CustomerDetail' && !$route.path.includes('/sime')
        "
        :totalData="totalData"
      />

      <Statistics
        v-if="!isComp"
        :border-line="isComp"
        :list="[
          { num: tableRef ? tableRef.staticTotal : 0, text: '威胁事件数' },
        ]"
      ></Statistics>
      <SelSearch
        v-model="searchState.data"
        :menu-data="searchState.menuData"
        :items="searchState.items"
        :show-search-unit-tree="
          existDeptSearch && !hideSearchUnitTree && compType != 'siemSearch'
        "
        @search="search"
      >
        <ul class="table-action-btns">
          <li
            v-hasPermi="['event:add']"
            v-if="!isComp"
            class="operate-icon"
            @click="newlyAdded"
          >
            <SvgIcon :size="16" icon-class="plus-outline" />
            添加事件
          </li>

          <li
            v-if="!isComp"
            v-hasPermi="'event:export'"
            @click="exportList"
            class="operate-icon"
          >
            <SvgIcon :size="16" icon-class="undo" />
            导出
          </li>
        </ul>
      </SelSearch>

      <SelTable
        ref="tableRef"
        class="event-table"
        :columns="columns"
        :load-data="getTableData"
        :default-params="{ ...defaultParams, _abort: 1 }"
      >
        <template #title="{ row }">
          <div
            :class="
              (!isComp && row.syncSource != '2') || compType == 'siemSearch'
                ? 'xel-clickable'
                : ''
            "
            @click="toDetail(row)"
          >
            <span
              >Case #{{ row.eventNo }}-{{ row.titlePrefix }} &nbsp;&nbsp;{{
                row.title
              }}</span
            >
            <p class="common-tag-box" v-setTagRowWidth>
              <span v-for="tag in row.eventTagList" :key="tag.id">{{
                tag.tagName
              }}</span>
            </p>
          </div></template
        >
        <template v-if="isComp" #radio="scope">
          <el-radio v-model="eventId" :label="scope.row.id">
            <span></span
          ></el-radio>
        </template>
        <template #level="scope">
          <el-tag :type="levelData[scope.row.levelId]">{{
            scope.row.levelName
          }}</el-tag>
        </template>
        <template #taskProgress="{ row }">
          <el-progress
            v-if="row.groupCountTotle != 0"
            :percentage="(row.groupCount1 / row.groupCountTotle) * 100"
            :stroke-width="8"
            status="success"
          >
            <span style="font-size: 12px; color: #555" text
              >{{ row.groupCount1 }}/{{ row.groupCountTotle }}</span
            >
          </el-progress>
        </template>
        <template #isClose="{ row }">
          {{ row.isClose == "0" ? "未关闭" : "已关闭" }}
        </template>
        <template #actions="scope">
          <CoordBtns
            v-if="scope.row.syncSource != '2'"
            :scope="scope"
            :initiateProcess="initiateProcess"
            :openSecondaryDia="openSecondaryDia"
            addPermi="coprocess:event:add"
            viewPermi="coprocess:event:view"
          ></CoordBtns>
        </template>
      </SelTable>

      <!-- 发起协同处置 选择单位 - 弹窗  -->
      <SelectUnit
        type="event"
        :selectForm="selectForm"
        ref="selectUnitRef"
        @selectDiaSub="initiateProcessFun"
      />

      <!-- 发起后 弹窗 -->
      <SecondaryDia
        v-if="secondaryFlag"
        type="event"
        ref="secondaryDiaRef"
        :secondaryForm="secondaryForm"
        addPermi="coprocess:event:add"
        @selectDiaSub="initiateProcessFun"
      />

      <!-- 事件任务 -->
      <LogDialog
        :visible="logDialogStatus"
        :eventId="logDialogEventId"
        :esIds="esIds"
        :eventSearch="eventSearch"
        @close="logDialogStatus = false"
      />
    </section>
  </Component>
</template>
<script setup>
import {
  initTask,
  deleteEventTemplate as delItem,
} from "@/api/analyticalDisposal/runscript";
import EventTotal from "./components/eventTotal.vue";
import { getEventCountStatistics } from "@/api/event/event";
import { getGetList, getEventList } from "@/api/event/event";
import { getGetMergeList } from "@/api/workSpace/workSpace";
import { LEVEL_DATA } from "@/config/constant";
import { getMergeList } from "@/api/workSpace/alert";
import download from "@/utils/download";

import ResizeWrapper from "@/globalComponents/resizeWrapper.vue";

import { useSetAreaDeptParams } from "@/utils/areaDeptParams";
import useDeptTreeStore from "@/store/modules/deptTree";
import { regionTree } from "@/api/tbcz/event/event";

import usePermissionFlag from "@/utils/permissionFlag";

import useDeptCoordinated from "@/views/event/deptCoordinated/composition";
import useUser from "@/store/modules/user";
import LogDialog from "./components/logDialog.vue";

const leftTreeType = computed(() => {
  return useUser().leftTreeType;
});

const { getId, deptParams, areaDeptParams } = useSetAreaDeptParams(search);

let firstIn = true;
onMounted(() => {
  setTimeout(() => {
    firstIn = false;
  }, 100);
});
onActivated(() => {
  if (!firstIn) {
    search(false);
  }
});

const resizeRef = ref(null);

const route = useRoute();
const currentRouteName = route.name;

const activeMenu = currentRouteName == "CustomerDetail" ? "/customer/list" : "";

const props = defineProps({
  // 是否为组织人员管理内事件
  customerDeptId: {
    type: String,
    default: "",
  },
  //是否是组件引用
  isComp: {
    type: Boolean,
    default: false,
  },
  //组件引用类型
  compType: {
    type: [String],
    default: "",
  },
  //siem事件引用的esIds
  esIds: {
    type: Array,
    default: () => [],
  },
  //siem事件引用的查询条件
  eventSearch: {
    type: Object,
    default: () => ({}),
  },
});
const totalData = ref([
  {
    name: "事件研判总量",
    count: 0,
  },
  {
    name: "待关闭事件数量",
    count: 0,
  },
  {
    name: "驳回事件数量",
    count: 0,
  },
]);
function getTotalData(params = {}) {
  getEventCountStatistics(params).then((res) => {
    for (let i of res) {
      if (i.name === "eventCount") {
        totalData.value[0].count = i.count;
      } else if (i.name === "unClouseEventCount") {
        totalData.value[1].count = i.count;
      } else if (i.name === "rejectEventCount") {
        totalData.value[2].count = i.count;
      }
    }
  });
}
onMounted(() => {
  getTotalData();
});
onActivated(() => {
  getTotalData();
});
const getTableData = ref(null);
if (props.isComp) {
  if (props.compType == "workAlert" || props.compType == "mergeEvent") {
    getTableData.value = getGetMergeList;
  } else if (props.compType == "siemSearch") {
    getTableData.value = getEventList;
  }
} else {
  getTableData.value = getGetList;
}
//可合并事件列表参数
const defaultParams = computed(() => {
  let params = {};
  if (props.compType == "mergeEvent") {
    params.id = route.params.id;
    params.spare5 = 1;
  }
  if (route.query.priority) {
    params.levelId = route.query.priority * 1;
    params.findTerm = 6;
  }

  // 组织人员管理事件
  if (
    props.customerDeptId ||
    route.name == "EventList" ||
    route.name == "CustomerDetail"
  ) {
    params = { ...params, ...deptParams.value };
  }

  if (props.compType == "siemSearch") {
    params = { isClose: 0, syncSource: 0 };
  }

  return params;
});
watch(
  defaultParams,
  () => {
    nextTick(() => {
      if (
        defaultParams.value.params &&
        defaultParams.value.params.deptTreeId != null
      ) {
        searchState.data.deptTreeId =
          defaultParams.value.params && defaultParams.value.params.deptTreeId;
        searchState.data.params = defaultParams.value.params;
      }

      // search();
    });
  },
  { deep: true, immediate: true }
);

const eventId = ref("");
const router = useRouter();
const tableRef = ref();
const dialogTitle = ref("");
const searchState = reactive({
  data: {
    levelId: "",
    isClose: "",
    findTerm: "",
    title: "",
    deptTreeId: "",
    noticeStatus: "",
    syncSource: "0",
  },
  menuData: [
    {
      label: "数据来源",
      prop: "syncSource",
      dictName: "sync_source",
      hideInSiemSearch: true,
      span: 12,
    },
    {
      label: "事件级别",
      prop: "levelId",
      options: [],
      span: 12,
      dictName: "event_level",
    },
    {
      label: "事件状态",
      prop: "isClose",
      options: [],
      dictName: "event_close",
      hideInSiemSearch: true,
      span: 12,
    },
    {
      label: "是否已发起协同",
      prop: "noticeStatus",
      dictName: "whether",
      hideInSiemSearch: true,
      span: 12,
    },
    {
      label: "创建时间",
      prop: "findTerm",
      optionList: [
        { value: "1", label: "今日" },
        { value: "2", label: "48小时" },
        { value: "3", label: "72小时" },
        { value: "4", label: "近7日" },
        { value: "5", label: "近30日" },
        { value: "6", label: "近半年" },
      ],
    },
  ],
  items: [
    {
      is: "el-input",
      prop: "title",
      label: "关键字",
      span: 8,
      options: {
        placeholder: "请输入事件名称、受影响资产、可疑对象、IP关键字",
      },
    },
  ],
});

// 左侧树与dept查询联动
const hideSearchUnitTree = computed(() => {
  return resizeRef.value && resizeRef.value.hideSearchUnitTree;
});
const existDeptSearch = computed(() => {
  return useDeptTreeStore().existDeptSearch;
});

getQueryPriority();
//获取路由中的query参数
function getQueryPriority() {
  if (!searchState.data.levelId && route.query.priority) {
    searchState.data.levelId = route.query.priority;
    searchState.data.findTerm = 6;
    const _location = window.location.href.split("?")[0];
    window.history.replaceState({}, "", _location);
  }
}
const state = reactive({
  levelData: LEVEL_DATA,
});
const { formData } = toRefs(state);
const { levelData } = toRefs(state);
function resetFormData() {
  state.formData = {
    name: "",
    resource: "",
  };
}

// 搜索按钮
function search(initPageNum = true) {
  if (
    !hideSearchUnitTree.value ||
    (resizeRef.value && !searchState.data.deptTreeId)
  ) {
    useDeptTreeStore().updateDeptId(searchState.data.deptTreeId, route.name);
    if (useDeptTreeStore().existDeptSearch) {
      const deptTreeType =
        useDeptTreeStore().deptSelectData[route.name].deptTreeType;
      areaDeptParams.value.params = {
        deptTreeId: searchState.data.deptTreeId,
        deptTreeType: deptTreeType,
      };
    }
  }
  nextTick(() => {
    tableRef.value.reload(searchState.data, initPageNum);
  });
}

// 列表配置项
const columns = ref([
  {
    hide: !props.isComp,
    prop: "id",
    label: "",
    slotName: "radio",
    width: "80px",
  },
  {
    prop: "title",
    label: "事件名称",
    slotName: "title",
    className: "",
    width: !props.isComp ? 250 : "",
  },

  {
    prop: "levelId",
    label: "事件级别",
    slotName: "level",
    width: 100,
  },
  {
    prop: "levelId",
    label: "任务",
    slotName: "taskProgress",

    width: !props.isComp ? 180 : "",
  },
  {
    prop: "isClose",
    label: "事件状态",
    slotName: "isClose",
    width: 90,
  },
  {
    prop: "deptName",
    label: "责任单位",
    hide: route.name !== "EventList",
  },
  {
    prop: "createName",
    label: "分析师",
    width: 90,
  },

  {
    prop: "assetName",
    label: "受影响资产",
  },
  {
    prop: "spare1",
    label: "可疑对象",
  },

  {
    prop: "createTime",
    label: "创建时间",
    width: 150,
  },
]);

function toDetail(row) {
  if (!props.isComp && row.syncSource != "2") {
    router.push({
      name: "EventDetail",
      //EventDetail
      params: {
        id: row.id,
      },
      query: {
        activeMenu,
      },
    }); //路由跳转
  } else if (props.compType == "siemSearch") {
    openLogDialog(row.id);
  }
}

// 列表操作方法
// 新增按钮
function newlyAdded() {
  router.push({
    name: "AddEvent",
    query: {
      activeMenu,
    },
  }); //路由跳转
}

//导出
function exportList() {
  const params = {};
  for (const key in searchState.data) {
    if (searchState.data[key] !== "") {
      params[key] = searchState.data[key];
    }
  }
  download("/alertEvent/event/exportEvent", params, "威胁事件.xlsx", true);
}

/* 发起协同处置 */
const {
  SelectUnit,
  SecondaryDia,
  selectUnitRef,
  selectForm,
  initiateProcessFun,
  initiateProcess,
  secondaryForm,
  secondaryDiaRef,
  secondaryFlag,
  openSecondaryDia,
  CoordBtns,
} = useDeptCoordinated(search, "event");

const regionTreeFlag = ref(false);
const regionTreeOptions = ref([]);

pushActionWithPermi();

function pushActionWithPermi() {
  if (
    !props.isComp &&
    (usePermissionFlag("coprocess:event:add") ||
      usePermissionFlag("coprocess:event:view"))
  ) {
    columns.value.push({
      width: 130,
      slotName: "actions",
      label: "操作",
    });
  }
}

function getIdFn(id, type, params) {
  getId(id, type, params);
  getTotalData(params);
}

/**siem查询分析内的事件引用 */
if (props.compType == "siemSearch") {
  searchState.menuData = searchState.menuData
    .filter((item) => !item.hideInSiemSearch)
    .map((item) => {
      if (item.prop == "findTerm") {
        const optionList = [...item.optionList];
        optionList.splice(-2, 0, {
          value: "7",
          label: "近15日",
        });
        return {
          ...item,
          optionList,
        };
      } else {
        return item;
      }
    });

  searchState.items.unshift({
    label: "责任单位",
    prop: "deptTreeId",
    is: "dept-tree",
    span: 8,
    option: {
      spare1: 2,
    },
  });
  searchState.data.syncSource = "0";
  searchState.data.isClose = "0";
  columns.value.splice(0, 1);
  columns.value.splice(3, 0, {
    prop: "deptName",
    label: "责任单位",
    width: 120,
  });
}
const logDialogStatus = ref(false);
const logDialogEventId = ref("");
function openLogDialog(eventId) {
  logDialogStatus.value = true;
  logDialogEventId.value = eventId;
}

watch(
  () => searchState.data.deptTreeId,
  (val) => {
    if (props.compType == "siemSearch") {
      searchState.data.params = {};
      if (val) {
        searchState.data.params.deptTreeId = val;
        searchState.data.params.deptTreeType = 2;
      }
    }
  }
);

defineExpose({
  eventId: computed(() => {
    return eventId.value;
  }),
  eventInfo: computed(() => {
    if (tableRef.value) {
      return tableRef.value.data.find((item) => {
        return item.id == eventId.value;
      });
    } else {
      return {};
    }
  }),
  search,
});
</script>
<style scoped lang="scss">
.upload-button {
  margin-right: 10px;
}
</style>
