<template>
  1
  <AssetListDynamic
    type="manage"
    active-menu="/assets/list/safeBusiness"
    :search-options="searchOptions"
    :fixed-searchfields="fixedSearchfields"
    :permis="permis"
    :get-all-columns-api="getGetAllAssetCustomFormList"
    :get-show-colums-by-user="getGetShowColumsByUser"
    :get-table-list="getGetAssetAccountList"
    :save-show-colums="postAddAssetShowColums"
    :del-api="postDeleteAssetAccount"
    download-template-url="/portal/assetAccount/downloadTemplate"
    import-url="/portal/assetAccount/importData"
    export-url="/portal/assetAccount/exportAsset"
  >
  </AssetListDynamic>
</template>
<script setup lang="ts" name="SafeBusiness">
import {
  getGetAllAssetCustomFormList,
  getGetShowColumsByUser,
  getGetAssetAccountList,
  postAddAssetShowColums,
  postDeleteAssetAccount,
} from "@/api/assets/list";

const fixedSearchfields = ref([
  "isOwn",
  "assetName",
  "url",
  "ip",
  "port",
  "protocolType",
  "icp",
  "publicRecord",
  "deploymentAddress",
  "lanIpPortProtocolType",
  "netIpPortProtocolType",
]);

const permis = {
  edit: "asset:account:edit",
  del: "asset:account:delete",
  add: "asset:account:add",
  import: "asset:account:import",
  export: "asset:account:export",
};

const searchOptions = ref({
  items: [
    {
      prop: "assetName",
      label: "资产名称",
      is: "el-input",
      span: 8,
    },
    {
      prop: "url",
      label: "业务入口",
      is: "el-input",
      span: 8,
    },
    {
      prop: "ip",
      label: "IP",
      is: "el-input",
      span: 8,
    },
    {
      prop: "port",
      label: "端口",
      is: "el-input",
      span: 8,
    },
    {
      prop: "protocolType",
      label: "服务协议",
      is: "el-select",
      option: {
        dictName: "service_agreement",
      },
      span: 8,
    },

    {
      prop: "isOwn",
      label: "是否为有主资产",
      is: "el-select",
      option: {
        dictName: "whether",
      },
      span: 8,
    },
  ],
  // menuData: [{ label: "是否为有主资产", prop: "isOwn", dictName: "whether" }],
});
</script>
<style lang="scss" scoped></style>
