<!--资产动态列展示： 安全资产台账=>计算设备资产  -->

<template>
  1
  <AssetListDynamic
    type="manage"
    active-menu="/assets/list/safeResource"
    :search-options="searchOptions"
    :fixed-searchfields="fixedSearchfields"
    :permis="permis"
    :get-all-columns-api="getGetAllAssetResourceCustomFormList"
    :get-show-colums-by-user="getVulnAssetResourceGetShowColumnsByUser"
    :get-table-list="getGetAssetResourceListDynamic"
    :save-show-colums="postAddAssetResourceShowColumns"
    :del-api="postResourceRemove"
    download-template-url="/portal/assetResource/downloadTemplate"
    import-url="/portal/assetResource/importData"
    export-url="/portal/assetResource/exportData"
    asset-type="2"
  >
  </AssetListDynamic>
</template>
<script setup lang="ts" name="SafeResource">
import {
  getVulnAssetResourceGetShowColumnsByUser,
  getGetAllAssetResourceCustomFormList,
  postAddAssetResourceShowColumns,
  getGetAssetResourceListDynamic,
  postResourceRemove,
} from "@/api/assets/resource";

interface Props {
  deptId?: string | undefined; //单位id，单位详情下的资产台账
}
withDefaults(defineProps<Props>(), {
  deptId: undefined,
});

const fixedSearchfields = ref([
  "isOwn",
  "assetName",
  "ip",
  "port",
  "deploymentAddress",
  "lanIpPortProtocolType",
  "netIpPortProtocolType",
  "softwareRelStr",
]);

const permis = {
  edit: "asset:resource:edit",
  del: "asset:resource:delete",
  add: "asset:resource:add",
  import: "asset:resource:import",
  export: "asset:resource:export",
};

const searchOptions = ref({
  items: [
    {
      prop: "assetName",
      label: "资产名称",
      is: "el-input",
    },

    {
      prop: "ipStr",
      label: "IP",
      is: "el-input",
    },
    {
      prop: "port",
      label: "端口",
      is: "el-input",
    },

    {
      prop: "isOwn",
      label: "是否为有主资产",
      is: "el-select",
      option: {
        dictName: "whether",
      },
    },
  ],
  // menuData: [{ label: "是否为有主资产", prop: "isOwn", dictName: "whether" }],
});
</script>
<style lang="scss" scoped></style>
