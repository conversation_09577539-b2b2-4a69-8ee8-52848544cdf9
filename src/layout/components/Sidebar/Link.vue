<template>
  <component :is="type" v-bind="linkProps()">
    <slot />
  </component>
</template>

<script setup>
import { isExternal } from "@/utils/validate";

const props = defineProps({
  to: {
    type: [String, Object],
    required: true,
  },
});

const isExt = computed(() => {
  return isExternal(props.to);
});

const type = computed(() => {
  if (isExt.value) {
    return "a";
  }
  return "router-link";
});

function linkProps() {
  if (isExt.value) {
    //大屏页面跳转单独处理
    console.log(props.to);
    if (props.to.includes("://isoss-screen")) {
      let href = "";
      // 构建完整的 URL，包含当前页面的协议、域名和端口
      const currentOrigin = window.location.origin;

      if (props.to.includes("://isoss-screen/synthesizeScreen")) {
        href =
          currentOrigin +
          "/isoss-screen/synthesizeScreen/?systemId=" +
          sessionStorage.getItem("systemId");
      } else if (props.to.includes("://isoss-screen/threadScreen")) {
        href =
          currentOrigin +
          "/isoss-screen/threadScreen/?systemId=" +
          sessionStorage.getItem("systemId");
      } else {
        href =
          currentOrigin +
          "/isoss-screen/?systemId=" +
          sessionStorage.getItem("systemId");
      }
      return {
        href: href,
        target: "_blank",
        rel: "noopener",
      };
    }

    return {
      href: props.to,
      target: "_blank",
      rel: "noopener",
    };
  }
  return {
    to: props.to,
  };
}
</script>
