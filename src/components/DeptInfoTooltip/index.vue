<template>
  <div class="dept-info-tooltip-wrapper">
    <el-tooltip
      :visible="tooltipVisible"
      :content="tooltipContent"
      placement="right-start"
      effect="light"
      trigger="manual"
      :show-arrow="true"
      :offset="15"
      :hide-after="0"
      :enterable="true"
      popper-class="dept-info-tooltip-popper"
      :virtual-ref="tooltipTriggerRef"
      virtual-triggering
    >
      <template #content>
        <div
          class="dept-tooltip-content"
          v-loading="loading"
          @mouseenter="handleTooltipMouseEnter"
          @mouseleave="handleTooltipMouseLeave"
        >
          <div
            v-if="
              !loading &&
              (deptData.analysts.length > 0 || deptData.services.length > 0)
            "
            class="content-wrapper"
          >
            <!-- 分析师配置信息 -->
            <div
              v-if="deptData.analysts.length > 0"
              class="section analysts-section"
            >
              <div class="section-title">分析师配置</div>
              <div class="section-content">
                <div
                  v-for="(analysts, roleName) in groupedAnalysts"
                  :key="roleName"
                  class="role-group"
                >
                  <div class="role-name">{{ roleName }}</div>
                  <div class="analyst-item">
                    {{
                      analysts.length
                        ? analysts.map((analyst) => analyst.nickName).join("，")
                        : "-"
                    }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 服务项管理信息 -->
            <div
              v-if="deptData.services.length > 0"
              class="section services-section"
            >
              <div class="section-title">服务项</div>
              <div class="section-content">
                <div
                  v-for="service in deptData.services"
                  :key="service.id"
                  class="service-group"
                >
                  <div class="service-parent">{{ service.serviceName }}</div>
                  <div
                    v-for="child in service.childDeptServiceRelList || []"
                    :key="child.id"
                    class="service-item"
                  >
                    <div class="service-name">{{ child.serviceName }}</div>
                    <div class="service-info">
                      {{ formatChildServiceTime(child) }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-else-if="!loading" class="no-data">暂无配置信息</div>
        </div>
      </template>
    </el-tooltip>

    <span
      ref="triggerRef"
      class="dept-name-link"
      @mouseenter="handleMouseEnter"
      @mouseleave="handleMouseLeave"
      @click="handleClick"
    >
      <slot>{{ deptName || "-" }}</slot>
    </span>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted } from "vue";
import { useRouter } from "vue-router";
import { debounce } from "lodash";
import { getAnalystList } from "@/api/customer/setOperation/analyst";
import { getSelectSysDeptServiceList } from "@/api/system/serviceitem";

interface Props {
  deptId: string; // 单位ID
  deptName: string; // 单位名称
}

const props = withDefaults(defineProps<Props>(), {
  deptId: "",
  deptName: "",
});

const router = useRouter();

// 响应式数据
const loading = ref(false);
const tooltipVisible = ref(false);
const triggerRef = ref<HTMLElement>();
const tooltipTriggerRef = ref({
  getBoundingClientRect() {
    return triggerRef.value?.getBoundingClientRect() || new DOMRect();
  },
});

// 数据存储
const deptData = ref<{
  analysts: any[];
  services: any[];
}>({
  analysts: [],
  services: [],
});

// 按角色分组分析师
const groupedAnalysts = computed(() => {
  const roleOrder = [
    "一线分析师",
    "二线分析师",
    "三线分析师",
    "交付经理",
    "总监",
  ];
  const groups: Record<string, any[]> = {};

  deptData.value.analysts.forEach((analyst) => {
    const roleName = analyst.roleName || "其他";
    if (!groups[roleName]) {
      groups[roleName] = [];
    }
    groups[roleName].push(analyst);
  });

  // 按预定义顺序排序角色，只返回存在的角色
  const sortedGroups: Record<string, any[]> = {};
  roleOrder.forEach((role) => {
    if (groups[role] && groups[role].length > 0) {
      sortedGroups[role] = groups[role];
    }
  });

  // 添加其他未预定义的角色
  Object.keys(groups).forEach((role) => {
    if (!roleOrder.includes(role) && groups[role].length > 0) {
      sortedGroups[role] = groups[role];
    }
  });

  return sortedGroups;
});

// 格式化子服务项信息，与单位详情页保持一致
const formatChildServiceTime = (child: any) => {
  if (child.type === "0") {
    // 时间段类型 - 显示服务数量和时间
    return `服务数量: ${child.num || 1}  ${child.startTime}  ${child.endTime}`;
  } else if (child.type === "1") {
    // 次数类型 - 显示服务数量和服务次数
    return `服务数量: ${child.num || 1} 服务次数: ${child.num}/${child.nums}`;
  } else {
    // 其他类型 - 显示时间信息
    return child.startTime && child.endTime
      ? ` ${child.startTime}  ${child.endTime}`
      : "时间待定";
  }
};

// 定时器
let mouseEnterTimer: NodeJS.Timeout | null = null;
let mouseLeaveTimer: NodeJS.Timeout | null = null;

// 数据是否已加载
const dataLoaded = ref(false);

// 鼠标进入事件处理函数（内部实现）
const handleMouseEnterInternal = async () => {
  // 清除离开定时器
  if (mouseLeaveTimer) {
    clearTimeout(mouseLeaveTimer);
    mouseLeaveTimer = null;
  }

  // 设置进入定时器
  mouseEnterTimer = setTimeout(async () => {
    if (!props.deptId) return;

    tooltipVisible.value = true;

    // 如果数据未加载，则加载数据
    if (!dataLoaded.value) {
      await loadDeptData();
    }
  }, 100); // 悬停100ms后显示
};

// 防抖优化：避免频繁触发鼠标进入事件
const handleMouseEnter = debounce(handleMouseEnterInternal, 50);

// 鼠标离开事件
const handleMouseLeave = () => {
  // 清除进入定时器
  if (mouseEnterTimer) {
    clearTimeout(mouseEnterTimer);
    mouseEnterTimer = null;
  }

  // 设置离开定时器
  mouseLeaveTimer = setTimeout(() => {
    tooltipVisible.value = false;
  }, 500); // 延迟200ms隐藏
};

// tooltip 内容鼠标进入事件
const handleTooltipMouseEnter = () => {
  // 清除离开定时器，保持 tooltip 显示
  if (mouseLeaveTimer) {
    clearTimeout(mouseLeaveTimer);
    mouseLeaveTimer = null;
  }
};

// tooltip 内容鼠标离开事件
const handleTooltipMouseLeave = () => {
  // 设置离开定时器
  mouseLeaveTimer = setTimeout(() => {
    tooltipVisible.value = false;
  }, 200); // 延迟200ms隐藏
};

// 加载单位数据
const loadDeptData = async () => {
  if (!props.deptId || loading.value) return;

  loading.value = true;
  let completedRequests = 0;
  const totalRequests = 2;

  try {
    // 独立处理分析师配置请求
    getAnalystList({ deptId: props.deptId })
      .then((response: any) => {
        deptData.value.analysts = response.rows || [];
      })
      .catch((error) => {
        console.error("获取分析师配置失败:", error);
        deptData.value.analysts = [];
      })
      .finally(() => {
        completedRequests++;
        if (completedRequests === totalRequests) {
          loading.value = false;
          dataLoaded.value = true;
        }
      });

    // 独立处理服务项管理请求
    getSelectSysDeptServiceList({ deptId: props.deptId, openState: "1" })
      .then((response: any) => {
        deptData.value.services = response.rows || [];
      })
      .catch((error) => {
        console.error("获取服务项配置失败:", error);
        deptData.value.services = [];
      })
      .finally(() => {
        completedRequests++;
        if (completedRequests === totalRequests) {
          loading.value = false;
          dataLoaded.value = true;
        }
      });
  } catch (error) {
    console.error("加载单位数据失败:", error);
    loading.value = false;
  }
};

// 点击事件 - 跳转到客户详情页面
const handleClick = () => {
  if (!props.deptId) return;

  const routeData = router.resolve({
    name: "CustomerDetail",
    params: { id: props.deptId },
  });
  window.open(routeData.href, "_blank");
};

// 组件卸载时清理定时器
onUnmounted(() => {
  if (mouseEnterTimer) {
    clearTimeout(mouseEnterTimer);
  }
  if (mouseLeaveTimer) {
    clearTimeout(mouseLeaveTimer);
  }
});

// tooltip内容（用于无数据时的简单显示）
const tooltipContent = computed(() => {
  if (loading.value) return "加载中...";
  if (
    deptData.value.analysts.length === 0 &&
    deptData.value.services.length === 0
  ) {
    return "暂无配置信息";
  }
  return "";
});
</script>

<style lang="scss" scoped>
.dept-info-tooltip-wrapper {
  display: inline-block;
}

.dept-name-link {
  color: var(--primary-color);
  cursor: pointer;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
}
</style>

<style lang="scss">
.dept-info-tooltip-popper {
  max-width: 800px !important;
  max-height: 500px !important;
  overflow-y: auto !important;
  min-width: 500px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;

  .dept-tooltip-content {
    padding: 20px;
    font-size: 12px;
    line-height: 1.6;

    .content-wrapper {
      display: flex;
      gap: 24px;
      align-items: flex-start;
    }

    .section {
      flex: 1;
      min-width: 0;

      &.analysts-section {
        flex: 0 0 280px;
      }

      &.services-section {
        flex: 1;
        min-width: 300px;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }

    .section-title {
      font-weight: bold;
      // color: var(--primary-color);

      margin-bottom: 8px;
      font-size: 14px;
      border-bottom: 1px solid #eee;
      padding-bottom: 4px;
      position: relative;

      /*  &::after {
        content: "";
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 30px;
        height: 2px;
        background: #eee;
        border-radius: 1px;
      } */
    }

    .section-content {
      color: var(--text-color-sub);
      padding-left: 4px;
    }

    .role-group {
      margin-bottom: 8px;
      padding: 8px 0px;
      background: var(--bg-color-page);
      border-radius: 6px;
      // border-left: 3px solid var(--primary-color);

      &:last-child {
        margin-bottom: 0;
      }
    }

    .role-name {
      font-weight: bold;
      // color: var(--primary-color);
      margin-bottom: 4px;
      font-size: 13px;
    }

    .analyst-item {
      color: var(--text-color-sub);
      font-size: 12px;
      line-height: 1.4;
    }

    .service-group {
      margin-bottom: 10px;
      padding: 8px 0px;
      background: var(--bg-color-page);
      border-radius: 6px;
      border: 1px solid var(--border-color-lighter);

      &:last-child {
        margin-bottom: 0;
      }
    }

    .service-parent {
      font-weight: bold;
      color: var(--text-color);
      margin-bottom: 6px;
      font-size: 13px;
      padding-bottom: 3px;
      border-bottom: 1px solid var(--border-color-lighter);
    }

    .service-item {
      margin-bottom: 6px;
      padding: 4px 6px;
      background: var(--bg-color);
      border-radius: 4px;
      border-left: 2px solid var(--success-color);

      &:last-child {
        margin-bottom: 0;
      }

      .service-name {
        font-weight: 500;
        color: var(--text-color);
        margin-bottom: 1px;
        font-size: 12px;
      }

      .service-info {
        color: var(--text-color-sub);
        font-size: 11px;
        line-height: 1.3;
      }
    }

    .no-data {
      color: var(--placeholder);
      text-align: center;
      padding: 40px 0;
      font-size: 14px;
      font-style: italic;
    }
  }
}
</style>
