<template>
  <div class="task-info-tooltip-wrapper">
    <el-tooltip
      :visible="tooltipVisible"
      :content="tooltipContent"
      placement="right-start"
      effect="light"
      trigger="manual"
      :show-arrow="true"
      :offset="15"
      :hide-after="0"
      :enterable="true"
      popper-class="task-info-tooltip-popper"
      :virtual-ref="tooltipTriggerRef"
      virtual-triggering
    >
      <template #content>
        <div
          class="task-tooltip-content"
          v-loading="loading"
          @mouseenter="handleTooltipMouseEnter"
          @mouseleave="handleTooltipMouseLeave"
        >
          <div
            v-if="
              !loading && (taskData.matrixName || taskData.scopes.length > 0)
            "
            class="content-wrapper"
          >
            <!-- 测试矩阵信息 -->
            <div v-if="taskData.matrixName" class="section matrix-section">
              <div class="section-title">
                测试矩阵： {{ taskData.matrixName }}
              </div>
            </div>

            <!-- 测试范围列表 -->
            <div
              v-if="taskData.scopes.length > 0"
              class="section scopes-section"
            >
              <div class="section-title">测试范围列表</div>
              <div class="section-content">
                <div
                  v-for="scope in formattedScopes"
                  :key="scope.id"
                  class="scope-group"
                >
                  <div class="scope-name">{{ scope.scopeName }}</div>
                  <div class="scope-details">
                    <div v-if="scope.hasTextContent" class="scope-item">
                      文本: {{ scope.content }}
                    </div>
                    <div v-if="scope.fileCount > 0" class="scope-item">
                      附件: {{ scope.fileCount }} 个
                    </div>
                    <div v-if="scope.ipCount > 0" class="scope-item">
                      IP: {{ scope.ipCount }} 个
                    </div>
                    <div
                      v-if="scope.businessSystemCount > 0"
                      class="scope-item"
                    >
                      业务系统: {{ scope.businessSystemCount }} 个
                    </div>
                    <div class="scope-item">
                      分析师: {{ scope.personNames || "-" }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-else-if="!loading" class="no-data">暂无任务信息</div>
        </div>
      </template>
    </el-tooltip>

    <span
      ref="triggerRef"
      class="task-name-link"
      @mouseenter="handleMouseEnter"
      @mouseleave="handleMouseLeave"
      @click="handleClick"
    >
      <slot>{{ taskName || "-" }}</slot>
    </span>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted } from "vue";
import { useRouter } from "vue-router";
import { debounce } from "lodash";
import { getTaskDetail } from "@/api/testManage/taskExecute";
import { getTaskScopeList } from "@/api/testManage/taskPlan";

interface Props {
  taskId: string; // 任务ID
  taskName: string; // 任务名称
}

const props = withDefaults(defineProps<Props>(), {
  taskId: "",
  taskName: "",
});

const router = useRouter();

// 响应式数据
const loading = ref(false);
const tooltipVisible = ref(false);
const triggerRef = ref();
const tooltipTriggerRef = computed(() => triggerRef.value);

// 任务数据
const taskData = ref({
  matrixName: "",
  scopes: [] as any[],
});

// 定时器
let mouseEnterTimer: NodeJS.Timeout | null = null;
let mouseLeaveTimer: NodeJS.Timeout | null = null;

// 数据是否已加载
const dataLoaded = ref(false);

// 格式化测试范围数据
const formattedScopes = computed(() => {
  console.log("TaskInfoTooltip - 开始格式化scopes数据:", taskData.value.scopes);

  return taskData.value.scopes.map((scope) => {
    const hasTextContent = !!scope.content;
    const fileCount = scope.testTaskScopeFileTotal || 0;
    const ipCount =
      (scope.testTaskScopeIpTotal || 0) +
      (scope.testTaskScopeIpCustomTotal || 0);
    const businessSystemCount =
      (scope.testTaskScopeUrlTotal || 0) +
      (scope.testTaskScopeUrlCustomTotal || 0);
    const personList = scope.testTaskPersonList || [];

    console.log(`TaskInfoTooltip - 范围 ${scope.scopeName} 的人员数据:`, {
      testTaskPersonList: scope.testTaskPersonList,
      testTaskPersonTotal: scope.testTaskPersonTotal,
      personList: personList,
    });

    const personNames = personList
      .map((person: any) => {
        console.log("TaskInfoTooltip - 处理人员:", person);
        return person.name || person.nickName || person.userName || "";
      })
      .filter((name) => name)
      .join(", ");

    console.log(
      `TaskInfoTooltip - 范围 ${scope.scopeName} 最终人员名称:`,
      personNames
    );

    return {
      id: scope.id,
      scopeName: scope.scopeName,
      hasTextContent,
      fileCount,
      ipCount,
      businessSystemCount,
      personNames: personNames,
      personCount: scope.testTaskPersonTotal || 0,
    };
  });
});

// 鼠标进入事件处理函数（内部实现）
const handleMouseEnterInternal = async () => {
  // 清除离开定时器
  if (mouseLeaveTimer) {
    clearTimeout(mouseLeaveTimer);
    mouseLeaveTimer = null;
  }

  // 设置进入定时器
  mouseEnterTimer = setTimeout(async () => {
    if (!props.taskId) return;

    tooltipVisible.value = true;

    // 如果数据未加载，则加载数据
    if (!dataLoaded.value) {
      await loadTaskData();
    }
  }, 100); // 悬停100ms后显示
};

// 防抖优化：避免频繁触发鼠标进入事件
const handleMouseEnter = debounce(handleMouseEnterInternal, 50);

// 鼠标离开事件
const handleMouseLeave = () => {
  // 清除进入定时器
  if (mouseEnterTimer) {
    clearTimeout(mouseEnterTimer);
    mouseEnterTimer = null;
  }

  // 设置离开定时器
  mouseLeaveTimer = setTimeout(() => {
    tooltipVisible.value = false;
  }, 500); // 延迟200ms隐藏
};

// tooltip 内容鼠标进入事件
const handleTooltipMouseEnter = () => {
  // 清除离开定时器，保持 tooltip 显示
  if (mouseLeaveTimer) {
    clearTimeout(mouseLeaveTimer);
    mouseLeaveTimer = null;
  }
};

// tooltip 内容鼠标离开事件
const handleTooltipMouseLeave = () => {
  // 设置离开定时器
  mouseLeaveTimer = setTimeout(() => {
    tooltipVisible.value = false;
  }, 200); // 延迟200ms隐藏
};

// 加载任务数据
const loadTaskData = async () => {
  if (!props.taskId || loading.value) return;

  loading.value = true;
  let completedRequests = 0;
  const totalRequests = 2;

  try {
    // 独立处理任务详情请求
    getTaskDetail({ id: props.taskId })
      .then((response: any) => {
        taskData.value.matrixName = response.matrixName || "";
      })
      .catch((error: any) => {
        console.error("获取任务详情失败:", error);
        taskData.value.matrixName = "";
      })
      .finally(() => {
        completedRequests++;
        if (completedRequests === totalRequests) {
          loading.value = false;
          dataLoaded.value = true;
        }
      });

    // 独立处理测试范围请求
    getTaskScopeList({
      taskId: props.taskId,
      personRows: undefined, // 获取全部责任人信息
    })
      .then((response: any) => {
        console.log("TaskInfoTooltip - 测试范围API响应:", response);
        taskData.value.scopes = response.taskScopeList || [];
        console.log(
          "TaskInfoTooltip - 处理后的scopes数据:",
          taskData.value.scopes
        );
      })
      .catch((error: any) => {
        console.error("获取测试范围失败:", error);
        taskData.value.scopes = [];
      })
      .finally(() => {
        completedRequests++;
        if (completedRequests === totalRequests) {
          loading.value = false;
          dataLoaded.value = true;
        }
      });
  } catch (error) {
    console.error("加载任务数据失败:", error);
    loading.value = false;
  }
};

// 点击事件 - 跳转到任务详情页面
const handleClick = () => {
  if (!props.taskId) return;

  const routeData = router.resolve({
    name: "TaskDetailPage",
    params: { id: props.taskId, taskType: "execute" },
  });
  window.open(routeData.href, "_blank");
};

// 组件卸载时清理定时器
onUnmounted(() => {
  if (mouseEnterTimer) {
    clearTimeout(mouseEnterTimer);
  }
  if (mouseLeaveTimer) {
    clearTimeout(mouseLeaveTimer);
  }
});

// tooltip内容（用于无数据时的简单显示）
const tooltipContent = computed(() => {
  if (loading.value) return "加载中...";
  if (!taskData.value.matrixName && taskData.value.scopes.length === 0) {
    return "暂无任务信息";
  }
  return "";
});
</script>

<style lang="scss" scoped>
.task-info-tooltip-wrapper {
  display: inline-block;
}

.task-name-link {
  color: var(--primary-color);
  cursor: pointer;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
}
</style>

<style lang="scss">
.task-info-tooltip-popper {
  max-width: 300px !important;
  max-height: 450px !important;
  overflow-y: auto !important;
  min-width: 300px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;

  .task-tooltip-content {
    padding: 20px;
    font-size: 12px;
    line-height: 1.6;

    .content-wrapper {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .section {
      width: 100%;

      &.matrix-section {
        padding: 4px 0;
      }

      &.scopes-section {
        padding: 4px 0;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }

    .section-title {
      font-weight: bold;
      // color: var(--primary-color);
      margin-bottom: 4px;
      font-size: 13px;
      border-bottom: 1px solid #eee;
      padding-bottom: 4px;
      position: relative;
    }

    .section-content {
      // color: var(--text-color-sub);
      // padding-left: 4px;
      font-size: 12px;
    }

    .matrix-item {
      padding: 2px 0px;
      background: var(--bg-color-page);
      border-radius: 6px;
      font-weight: 500;
      color: var(--text-color);
      font-size: 12px;
    }

    .scope-group {
      margin-bottom: 2px;
      padding: 4px 0;
      background: var(--bg-color-page);
      border-radius: 6px;
      border: 1px solid var(--border-color-lighter);

      &:last-child {
        margin-bottom: 0;
      }
    }

    .scope-name {
      font-weight: bold;
      color: var(--text-color);
      margin-bottom: 4px;
      font-size: 12px;
      padding-bottom: 2px;
      border-bottom: 1px solid var(--border-color-lighter);
    }

    .scope-details {
      .scope-item {
        margin-bottom: 2px;
        padding: 2px 0;
        color: var(--text-color);
        font-size: 12px;
        text-align: left;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .no-data {
      color: var(--placeholder);
      text-align: center;
      padding: 40px 0;
      font-size: 14px;
      font-style: italic;
    }
  }
}
</style>
