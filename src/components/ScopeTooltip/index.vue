<!--测试范围标题组件，支持鼠标悬停显示统计信息-->
<template>
  <span
    ref="triggerRef"
    class="scope-title-with-tooltip"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    &nbsp;&nbsp; {{ displayName }}
  </span>

  <!-- Tooltip -->
  <el-tooltip
    :visible="tooltipVisible"
    placement="right-start"
    effect="light"
    trigger="manual"
    :show-arrow="true"
    :offset="15"
    :hide-after="0"
    :enterable="true"
    popper-class="scope-info-tooltip-popper"
    :virtual-ref="tooltipTriggerRef"
    virtual-triggering
  >
    <template #content>
      <div
        class="scope-tooltip-content"
        @mouseenter="handleTooltipMouseEnter"
        @mouseleave="handleTooltipMouseLeave"
      >
        <div class="content-wrapper">
          <div class="section">
            <div class="section-title">测试范围统计</div>
            <div class="section-content">
              <div v-if="hasTextContent" class="scope-item">
                文本: {{ props.content }}
              </div>
              <div v-if="fileCount > 0" class="scope-item">
                附件: {{ fileCount }} 个
              </div>
              <div v-if="ipCount > 0" class="scope-item">
                IP: {{ ipCount }} 个
              </div>
              <div v-if="businessSystemCount > 0" class="scope-item">
                业务系统: {{ businessSystemCount }} 个
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </el-tooltip>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted } from "vue";
import { debounce } from "lodash";

/**
 * 责任单位接口
 */
interface ResponsibleDept {
  deptId: string;
  deptName: string;
}

/**
 * 组件Props接口
 */
interface Props {
  /** 测试范围名称 */
  scopeName: string;
  /** 责任单位列表（可选） */
  responsibleDepts?: Array<ResponsibleDept>;
  /** 文本内容 */
  content?: string;
  /** 附件数量 */
  testTaskScopeFileTotal?: number;
  /** IP数量 */
  testTaskScopeIpTotal?: number;
  /** 自定义IP数量 */
  testTaskScopeIpCustomTotal?: number;
  /** 业务系统数量 */
  testTaskScopeUrlTotal?: number;
  /** 自定义业务系统数量 */
  testTaskScopeUrlCustomTotal?: number;
}

const props = withDefaults(defineProps<Props>(), {
  responsibleDepts: () => [],
  content: "",
  testTaskScopeFileTotal: 0,
  testTaskScopeIpTotal: 0,
  testTaskScopeIpCustomTotal: 0,
  testTaskScopeUrlTotal: 0,
  testTaskScopeUrlCustomTotal: 0,
});

// 计算目标总数量
const totalTargetCount = computed(() => {
  const hasTextContent = !!props.content;
  const fileCount = props.testTaskScopeFileTotal || 0;
  const ipCount =
    (props.testTaskScopeIpTotal || 0) + (props.testTaskScopeIpCustomTotal || 0);
  const businessSystemCount =
    (props.testTaskScopeUrlTotal || 0) +
    (props.testTaskScopeUrlCustomTotal || 0);

  return (hasTextContent ? 1 : 0) + fileCount + ipCount + businessSystemCount;
});

// 检查是否有有效的责任单位数据
const hasValidResponsibleDepts = computed(() => {
  return (
    props.responsibleDepts &&
    Array.isArray(props.responsibleDepts) &&
    props.responsibleDepts.length > 0 &&
    props.responsibleDepts.some((dept) => dept && dept.deptId && dept.deptName)
  );
});

// 生成带责任单位和目标数量的范围标题
const displayName = computed(() => {
  let title = props.scopeName;

  // 只有在有有效责任单位数据时才添加责任单位信息
  if (hasValidResponsibleDepts.value) {
    const validDeptNames = props.responsibleDepts
      .filter((dept) => dept && dept.deptName) // 过滤掉无效数据
      .map((dept) => dept.deptName)
      .join(", ");

    if (validDeptNames) {
      title = `${title} (${validDeptNames})`;
    }
  }

  // 添加目标数量信息
  title = `${title} - 目标数量: ${totalTargetCount.value}`;

  return title;
});

// Tooltip 相关的计算属性
const hasTextContent = computed(() => !!props.content);
const fileCount = computed(() => props.testTaskScopeFileTotal || 0);
const ipCount = computed(
  () =>
    (props.testTaskScopeIpTotal || 0) + (props.testTaskScopeIpCustomTotal || 0)
);
const businessSystemCount = computed(
  () =>
    (props.testTaskScopeUrlTotal || 0) +
    (props.testTaskScopeUrlCustomTotal || 0)
);

// Tooltip 相关状态
const tooltipVisible = ref(false);
const triggerRef = ref<HTMLElement>();
const tooltipTriggerRef = computed(() => triggerRef.value);

// 定时器
let mouseEnterTimer: ReturnType<typeof setTimeout> | null = null;
let mouseLeaveTimer: ReturnType<typeof setTimeout> | null = null;

// 鼠标进入事件处理函数（内部实现）
const handleMouseEnterInternal = () => {
  // 清除离开定时器
  if (mouseLeaveTimer) {
    clearTimeout(mouseLeaveTimer);
    mouseLeaveTimer = null;
  }

  // 设置进入定时器
  mouseEnterTimer = setTimeout(() => {
    tooltipVisible.value = true;
  }, 100); // 悬停100ms后显示
};

// 防抖优化：避免频繁触发鼠标进入事件
const handleMouseEnter = debounce(handleMouseEnterInternal, 50);

// 鼠标离开事件
const handleMouseLeave = () => {
  // 清除进入定时器
  if (mouseEnterTimer) {
    clearTimeout(mouseEnterTimer);
    mouseEnterTimer = null;
  }

  // 设置离开定时器
  mouseLeaveTimer = setTimeout(() => {
    tooltipVisible.value = false;
  }, 500); // 延迟500ms隐藏
};

// tooltip 内容鼠标进入事件
const handleTooltipMouseEnter = () => {
  // 清除离开定时器，保持 tooltip 显示
  if (mouseLeaveTimer) {
    clearTimeout(mouseLeaveTimer);
    mouseLeaveTimer = null;
  }
};

// tooltip 内容鼠标离开事件
const handleTooltipMouseLeave = () => {
  // 设置离开定时器
  mouseLeaveTimer = setTimeout(() => {
    tooltipVisible.value = false;
  }, 200); // 延迟200ms隐藏
};

// 组件卸载时清理定时器
onUnmounted(() => {
  if (mouseEnterTimer) {
    clearTimeout(mouseEnterTimer);
  }
  if (mouseLeaveTimer) {
    clearTimeout(mouseLeaveTimer);
  }
});
</script>

<style scoped lang="scss">
.scope-title-with-tooltip {
  cursor: pointer;

  &:hover {
    color: var(--primary-color);
  }
}
</style>

<style lang="scss">
// 全局样式，用于 tooltip popper
.scope-info-tooltip-popper {
  max-width: 400px !important;
  max-height: 450px !important;
  overflow-y: auto !important;
  min-width: 400px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;

  .scope-tooltip-content {
    padding: 20px;
    font-size: 12px;
    line-height: 1.6;

    .content-wrapper {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .section {
      width: 100%;
      padding: 4px 0;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .section-title {
      font-weight: bold;
      margin-bottom: 4px;
      font-size: 13px;
      border-bottom: 1px solid #eee;
      padding-bottom: 4px;
      position: relative;
    }

    .section-content {
      font-size: 12px;
    }

    .scope-item {
      margin-bottom: 2px;
      padding: 2px 0;
      color: var(--text-color);
      font-size: 12px;
      text-align: left;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>
