<template>
  <div class="table-pagination-new">
    <el-table
      ref="table"
      :data="state.list"
      v-bind="$attrs"
      v-loading="loading"
      style="width: 100%"
      @sortChange="sortChange"
      :row-key="rowKey"
    >
      <el-table-column
        v-if="checkbox"
        :reserve-selection="true"
        type="selection"
        width="55"
      ></el-table-column>

      <template
        v-for="(columnItem, index) in columns.filter((item) => !item.hide)"
        :key="index"
      >
        <!-- 正常的el-table-column -->
        <el-table-column
          v-if="!(columnItem.slotName || columnItem.click || columnItem.html)"
          :show-overflow-tooltip="true"
          v-bind="columnItem"
        ></el-table-column>
        <!-- 渲染为html的列 -->
        <el-table-column
          v-else-if="columnItem.html"
          :show-overflow-tooltip="true"
          v-bind="columnItem"
        >
          <template #default="scope">
            <div
              class="ellipsis"
              v-html="$replaceToken(scope.row.description)"
            ></div>
          </template>
        </el-table-column>
        <!-- 继承插槽 -->
        <el-table-column
          v-else-if="columnItem.slotName"
          v-bind="columnItem"
          :width="
            columnItem.slotName == 'actionBtns'
              ? columnItem.btnsWidth
                ? columnItem.btnsWidth
                : actionBtnsWidth
              : columnItem.width
          "
        >
          <template #default="scope">
            <!-- 操作列按钮组插槽 -->
            <xel-handle-btns
              v-if="columnItem.slotName == 'actionBtns'"
              ref="btnsRef"
              :btn-list="columnItem.btnList"
              :scope="scope"
            ></xel-handle-btns>
            <!-- 普通插槽 -->
            <slot
              v-if="columnItem.slotName != 'actionBtns' && scope.$index > -1"
              :name="columnItem.slotName"
              :row="scope.row"
              :column="scope.column"
              :$index="scope.$index"
            ></slot>
          </template>
        </el-table-column>
        <!-- 可点击的列 -->
        <el-table-column v-else-if="columnItem.click" v-bind="columnItem">
          <template #default="scope">
            <div class="xel-clickable" @click="columnItem.click(scope)">
              {{
                columnItem.formatter
                  ? columnItem.formatter(scope)
                  : scope.row[columnItem.prop]
              }}
            </div>
          </template>
        </el-table-column>
        <!-- 跳转到单位详情页面 -->
        <el-table-column
          v-else-if="
            columnItem.prop == 'deptName' ||
            columnItem.prop == 'eventTitle' ||
            columnItem.clickDept
          "
          v-hasKey="columnItem.props"
          :class-name="showToolTip ? 'column-tooltip' : ''"
          v-bind="columnItem"
        >
          <template #default="scope">
            <div
              v-clickDept="[
                columnItem?.clickDept ? columnItem.clickDept : columnItem.prop,
                scope.row,
              ]"
              class="sel-clickable"
            >
              跳转{{ scope.row[columnItem.prop] }}
            </div>
          </template></el-table-column
        >
      </template>
    </el-table>
    <!-- 显示名称 -->
    <div class="dialog-footer" style="overflow: hidden">
      <div class="Count">
        <slot name="tableFooter"> </slot>
      </div>
      <xel-pagination
        v-if="pagination"
        v-show="total > 5"
        ref="paginationRef"
        class="xel-table-pagination"
        :total="total"
        :page-size="pageSize"
        :page-sizes="pageSizes"
        :normalPage="normalPage"
        @change="changePagination"
      ></xel-pagination>
    </div>
  </div>
</template>
<script setup>
import { ref, reactive, computed, nextTick, watch } from "vue";
import xelHandleBtns from "./xelHandleBtns.vue";
import xelPagination from "./xelPagination.vue";
import Sortable from "sortablejs";

let props = defineProps({
  //列数组-el-table-column
  columns: {
    type: Array,
    required: true,
  },
  data: {
    type: [Array, null],
    default() {
      return null;
    },
  },
  //获取数据的方法
  loadData: {
    type: Function,
    required: true,
  },
  //是否处理接口返回数据
  handleData: {
    type: [Function, null],
    default: () => {
      return null;
    },
  },
  //表格是否显示复选框
  checkbox: {
    type: Boolean,
    default: false,
  },
  normalPage: {
    type: Boolean,
    default: false,
  },
  //是否显示分页
  pagination: {
    type: Boolean,
    default: true,
  },
  //每页的条数
  pageSize: {
    type: [Number, null],
    default: 10,
  },
  //每页条数列表
  pageSizes: {
    type: [Array, null],
    default() {
      return [10, 20, 50, 100];
    },
  },
  //查询请求的默认参数
  defaultParams: {
    type: Object,
    default() {
      return {};
    },
  },
  // 接口返回关键字
  resKey: {
    type: String,
    default: "",
  },
  rowKey: {
    type: String,
    default: "",
  },
  totalKey: {
    type: String,
    default: "total",
  },
});
//供父组件访问el-table
let total = ref(0);
let staticTotal = ref(0); //第一次请求的总量，不跟随查询条件改变
const table = ref();
let loading = ref(false);

let state = reactive({
  list: [],
  resData: {},
});

let paginationRef = ref();

let searchParams = {};
let pageParams = {};

let lastParams = {}; //排序使用
let sortParams = {};
function getData(params, initPageNum = true) {
  loading.value = true;
/*

*/
  //如果有参数，分页重置为1
  if (props.pagination && initPageNum && paginationRef.value) {
    paginationRef.value.resetPageNum();
  }
  if (paginationRef.value) {
    pageParams = paginationRef.value.pageData;
  }

  searchParams = params ? { ...params } : {};

  if (props.data) {
    state.list = props.data;

    loading.value = false;
    return;
  }

  lastParams = {
    ...props.defaultParams,
    ...searchParams,
    ...sortParams,
    ...pageParams,
  };
  props
    .loadData(lastParams)
    .then(({ data }) => {
      if (props.resKey) {
        state.list = data[props.resKey];
      } else {
        state.list = data.rows || data;
      }
      state.resData = data;
      if (props.handleData) {
        state.list = props.handleData(state.list); //处理返回数据
      }

      total.value = data[props.totalKey] || 0;

      //保存没有查询条件的总量
      if (!staticTotal.value) {
        staticTotal.value = total.value;
      }
      //删除后页码后退一个
      if (
        props.pagination &&
        state.list.length == 0 &&
        pageParams.pageNum != 1
      ) {
        paginationRef.value.pageNum--;
      }
    })
    .finally(() => {
      loading.value = false;
    });
}

if (!props.pagination) {
  getData();
}
//分页改变 ，初始化加载数据
function changePagination(params) {
  pageParams = { ...params };

  let searchP = getSearchParams();

  getData(searchP, false);
}
//分页切换时获取搜索条件
function getSearchParams() {
  let lasetSearchParams = { ...lastParams };
  delete lasetSearchParams.pageNum;
  delete lasetSearchParams.pageSize;
  delete lasetSearchParams.orderByColumn;
  delete lasetSearchParams.isAsc;
  return lasetSearchParams;
}

//表格操作列的宽度自适应
let btnsRef = ref();
let actionBtnsWidth = ref();
let btnWatch = watch(
  () => btnsRef.value,
  (val) => {
    if (btnsRef.value.$el) {
      actionBtnsWidth.value = btnsRef.value.$el.offsetWidth + 10 + "px";
    }

    nextTick(() => {
      table.value.doLayout();
    });
    btnWatch();
  }
);

function getDataList() {
  return state.list;
}

// 行拖拽
function rowDrop() {
  // 此时找到的元素是要拖拽元素的父容器
  const tbody = document.querySelector(".el-table__body-wrapper tbody");

  Sortable.create(tbody, {
    //  指定父元素下可被拖拽的子元素
    draggable: ".el-table__row",
    onEnd({ newIndex, oldIndex }) {
      const currRow = state.list.splice(oldIndex, 1)[0];
      state.list.splice(newIndex, 0, currRow);
    },
  });
}
watch(
  () => props.data,
  (val) => {
    getData();
    if (props.rowKey) {
      rowDrop();
    }
  }
);
//排序
function sortChange({ prop, order }) {
  sortParams.orderByColumn = prop;
  sortParams.isAsc = order == "descending" ? "desc" : "asc";
  getData({ ...lastParams, ...sortParams });
}

//父组件可以调用的方法
defineExpose({
  table: computed(() => {
    return table.value;
  }),
  reload: getData,
  data: computed(() => {
    return state.list || [];
  }),
  getDataList,
  resData: computed(() => {
    return state.resData;
  }),
  staticTotal: computed(() => {
    return state.resData.total;
  }),
});
</script>

<style lang="scss" scoped>
.xel-table-search {
  margin-top: 10px;
  margin-bottom: 10px;
}
.xel-search-btns {
  display: inline-block;
}
.dialog-footer {
  width: 100%;
  // display: flex;
  // justify-content: space-between;
}
.Count {
  width: 100%;
  float: left;
  line-height: 32px;
  background: #fff;
  padding: 15px 15px 0 15px;
}
.xel-table-pagination {
  float: right;
  // margin-top: 20px;
}
.xel-clickable {
  color: var(--secondary-color);
  cursor: pointer;
}
</style>
