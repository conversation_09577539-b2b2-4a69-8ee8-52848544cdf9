/*群模板*/
import request from "@/utils/request";
/*查询模板集合*/
export function getGetTemplateList(params: { [index: string]: any } = {}) {
  return request.get(`/system/groupTemplate/getTemplateList`, { params });
}
/*上传群图片模板*/
export function postUploadFileForGroup(params: { [index: string]: any } = {}) {
  return request.post(`/file/uploadFileForGroup`, params);
}
/*添加模板内容（文字、html模板）*/
export function postAddOrUpdateTemplate(params: { [index: string]: any } = {}) {
  return request.post(`/system/groupTemplate/addOrUpdateTemplate`, params);
}
