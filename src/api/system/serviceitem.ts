/*服务项管理*/
import request from "@/utils/request";
/*服务项树形列表*/
export function getTree(params: { [index: string]: any } = {}) {
  return request.get(`/system/serviceitem/tree`, { params });
}
/*服务项详情*/
export function getServiceitem(params: { [index: string]: any } = {}) {
  return request.get(`/system/serviceitem/${params.id}`, { params });
}
/*服务项删除*/
export function deleteRemove(params: { [index: string]: any } = {}) {
  return request.delete(`/system/serviceitem/remove/${params.id}`, { params });
}
/*服务项新增*/
export function postAdd(params: { [index: string]: any } = {}) {
  return request.post(`/system/serviceitem/add`, params);
}
/*服务项编辑*/
export function putEdit(params: { [index: string]: any } = {}) {
  return request.put(`/system/serviceitem/edit`, params);
}
/*获取所有服务项子类*/
export function getSelectSysServiceItemChildList(
  params: { [index: string]: any } = {}
) {
  return request.get(
    `/system/deptServiceRel/selectSysServiceItemChildList/${params.deptId}`,
    { params }
  );
}
/*保存单位关联服务项*/
export function putDeptServiceRel(params: { [index: string]: any } = {}) {
  return request.put(`/system/deptServiceRel`, params);
}
/*单位服务项列表*/
export function getSelectSysDeptServiceList(
  params: { [index: string]: any } = {}
) {
  return request.get(`/system/deptServiceRel/selectSysDeptServiceList`, {
    params,
  });
}
/*单位服务项已关联列表*/
export function getSelectSysDeptServiceItemList(
  params: { [index: string]: any } = {}
) {
  return request.get(`/system/deptServiceRel/selectSysDeptServiceItemList`, {
    params,
  });
}
/*查看服务变更历史记录*/
export function getSelectSysDeptServiceHistoryList(
  params: { [index: string]: any } = {}
) {
  return request.get(`/system/deptServiceRel/selectSysDeptServiceHistoryList`, {
    params,
  });
}

/*更新客户服务状态*/
export function updateCustomerServiceStatus(
  params: { [index: string]: any } = {}
) {
  return request.put(`/system/dept/changeCustomerType`, params);
}
